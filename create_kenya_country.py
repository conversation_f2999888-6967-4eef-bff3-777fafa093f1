#!/usr/bin/env python3

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, '/home/<USER>/job/gch_v2/gch-bench')

# Set the sites directory
os.environ['FRAPPE_SITES_PATH'] = '/home/<USER>/job/gch_v2/gch-bench/sites'

import frappe

def create_kenya_country():
    """Create Kenya country record if it doesn't exist"""
    
    # Initialize Frappe
    frappe.init(site='gch.local')
    frappe.connect()
    
    try:
        # Check if Kenya already exists
        if frappe.db.exists('Country', 'Kenya'):
            print("Kenya country record already exists")
            return
        
        # Create Kenya country record
        kenya_doc = frappe.get_doc({
            'doctype': 'Country',
            'country_name': 'Kenya',
            'name': 'Kenya',
            'code': 'KE',
            'date_format': 'dd-mm-yyyy',
            'time_format': 'HH:mm:ss',
            'time_zones': [
                {
                    'time_zone': 'Africa/Nairobi'
                }
            ]
        })
        
        kenya_doc.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ Kenya country record created successfully")
        
    except Exception as e:
        print(f"❌ Error creating Kenya country record: {str(e)}")
        frappe.db.rollback()
    
    finally:
        frappe.destroy()

if __name__ == "__main__":
    create_kenya_country()
