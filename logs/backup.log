set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250621_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-21 12:00:03.434820
Config  : ./gch.local/private/backups/20250621_120002-gch_local-site_config_backup.json 94.0B
Database: ./gch.local/private/backups/20250621_120002-gch_local-database.sql.gz         1.1MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250623_120001-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-23 12:00:05.155683
Config  : ./gch.local/private/backups/20250623_120001-gch_local-site_config_backup.json 94.0B
Database: ./gch.local/private/backups/20250623_120001-gch_local-database.sql.gz         1.7MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250624_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-24 12:00:03.764328
Config  : ./gch.local/private/backups/20250624_120002-gch_local-site_config_backup.json 98.0B
Database: ./gch.local/private/backups/20250624_120002-gch_local-database.sql.gz         1.8MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250625_120003-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-25 12:00:06.361781
Config  : ./gch.local/private/backups/20250625_120003-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250625_120003-gch_local-database.sql.gz         1.8MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250626_120003-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-26 12:00:08.149145
Config  : ./gch.local/private/backups/20250626_120003-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250626_120003-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250627_120003-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-27 12:00:07.344754
Config  : ./gch.local/private/backups/20250627_120003-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250627_120003-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250630_180053-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-06-30 18:00:57.903882
Config  : ./gch.local/private/backups/20250630_180053-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250630_180053-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250705_000004-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-05 00:00:08.599333
Config  : ./gch.local/private/backups/20250705_000004-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250705_000004-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250706_000003-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-06 00:00:05.280826
Config  : ./gch.local/private/backups/20250706_000003-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250706_000003-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250707_000004-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-07 00:00:07.151550
Config  : ./gch.local/private/backups/20250707_000004-gch_local-site_config_backup.json 121.0B
Database: ./gch.local/private/backups/20250707_000004-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250707_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-07 12:00:04.072541
Config  : ./gch.local/private/backups/20250707_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250707_120002-gch_local-database.sql.gz         1.8MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250708_000002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-08 00:00:04.694090
Config  : ./gch.local/private/backups/20250708_000002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250708_000002-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250708_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-08 12:00:05.825123
Config  : ./gch.local/private/backups/20250708_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250708_120002-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250709_000002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-09 00:00:04.039423
Config  : ./gch.local/private/backups/20250709_000002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250709_000002-gch_local-database.sql.gz         1.9MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250709_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-09 12:00:04.286386
Config  : ./gch.local/private/backups/20250709_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250709_120002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250710_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-10 12:00:04.278266
Config  : ./gch.local/private/backups/20250710_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250710_120002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250711_120003-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-11 12:00:09.944223
Config  : ./gch.local/private/backups/20250711_120003-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250711_120003-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250712_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-12 12:00:05.058102
Config  : ./gch.local/private/backups/20250712_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250712_120002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250713_000002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-13 00:00:05.568129
Config  : ./gch.local/private/backups/20250713_000002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250713_000002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250714_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-14 12:00:05.596769
Config  : ./gch.local/private/backups/20250714_120002-gch_local-site_config_backup.json 183.0B
Database: ./gch.local/private/backups/20250714_120002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250715_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-15 12:00:04.783437
Config  : ./gch.local/private/backups/20250715_120002-gch_local-site_config_backup.json 439.0B
Database: ./gch.local/private/backups/20250715_120002-gch_local-database.sql.gz         2.0MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250716_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-16 12:00:07.334223
Config  : ./gch.local/private/backups/20250716_120002-gch_local-site_config_backup.json 439.0B
Database: ./gch.local/private/backups/20250716_120002-gch_local-database.sql.gz         2.1MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250717_120002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-17 12:00:04.867763
Config  : ./gch.local/private/backups/20250717_120002-gch_local-site_config_backup.json 439.0B
Database: ./gch.local/private/backups/20250717_120002-gch_local-database.sql.gz         2.1MiB
Backup for Site gch.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_fa1b19b2f922012e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _fa1b19b2f922012e | /usr/bin/gzip >> ./gch.local/private/backups/20250718_060002-gch_local-database.sql.gz

Backup Summary for gch.local at 2025-07-18 06:00:08.377101
Config  : ./gch.local/private/backups/20250718_060002-gch_local-site_config_backup.json 439.0B
Database: ./gch.local/private/backups/20250718_060002-gch_local-database.sql.gz         2.1MiB
Backup for Site gch.local has been successfully completed
