2025-06-20 16:24:07,757 DEBUG cd gch-bench && python3 -m venv env
2025-06-20 16:24:09,838 DEBUG cd gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-06-20 16:24:12,153 DEBUG cd gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet wheel
2025-06-20 16:24:14,440 LOG Getting frappe
2025-06-20 16:24:14,440 DEBUG cd gch-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-20 16:24:26,757 LOG Installing frappe
2025-06-20 16:24:26,759 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/frappe 
2025-06-20 16:25:33,410 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/frappe && yarn install --check-files
2025-06-20 16:25:37,017 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 16:25:37,166 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 16:25:37,166 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 16:25:37,166 DEBUG cd gch-bench && bench build
2025-06-20 16:25:37,246 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build
2025-06-20 16:25:50,274 LOG setting up backups
2025-06-20 16:25:50,282 LOG backups were set up
2025-06-20 16:25:50,282 INFO Bench gch-bench initialized
2025-06-20 16:26:45,494 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app frappe --branch version-15
2025-06-20 16:26:53,130 INFO App moved from apps/frappe to archived/apps/frappe-2025-06-20
2025-06-20 16:26:53,135 LOG Getting frappe
2025-06-20 16:26:53,135 DEBUG cd ./apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-20 16:27:05,822 LOG Installing frappe
2025-06-20 16:27:05,823 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/frappe 
2025-06-20 16:27:07,910 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/frappe && yarn install --check-files
2025-06-20 16:27:11,008 DEBUG bench build --app frappe
2025-06-20 16:27:11,088 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app frappe
2025-06-20 16:27:23,855 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 16:27:24,003 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 16:27:24,003 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 16:32:54,707 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-20 16:33:10,002 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench setup requirements
2025-06-20 16:33:10,014 DEBUG /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-06-20 16:33:10,759 LOG Installing frappe
2025-06-20 16:33:10,760 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/frappe 
2025-06-20 16:33:32,128 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/frappe && yarn install --check-files
2025-06-20 16:33:41,624 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-20 16:34:10,673 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench new-site gch.local
2025-06-20 16:35:28,859 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app erpnext --branch version-15
2025-06-20 16:35:29,526 LOG Getting erpnext
2025-06-20 16:35:29,526 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-06-20 16:35:43,127 LOG Installing erpnext
2025-06-20 16:35:43,127 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/erpnext 
2025-06-20 16:35:49,871 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/erpnext && yarn install --check-files
2025-06-20 16:35:50,120 DEBUG bench build --app erpnext
2025-06-20 16:35:50,206 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app erpnext
2025-06-20 16:35:52,385 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 16:35:52,541 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 16:35:52,541 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 16:47:38,428 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-20 16:48:10,252 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench setup requirements
2025-06-20 16:48:10,255 DEBUG /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-06-20 16:48:10,979 LOG Installing frappe
2025-06-20 16:48:10,980 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/frappe 
2025-06-20 16:48:21,068 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/frappe && yarn install --check-files
2025-06-20 16:48:21,851 LOG Installing erpnext
2025-06-20 16:48:21,852 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/erpnext 
2025-06-20 16:48:24,779 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/erpnext && yarn install --check-files
2025-06-20 16:50:03,427 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app erpnext
2025-06-20 16:51:45,346 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-20 16:51:45,528 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-20 16:51:45,532 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-20 16:51:45,537 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-20 16:51:45,577 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-20 16:56:51,616 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_messaging gch_messaging **************:Gertrude-s-Children-s-Hospital/gch-messaging.git
2025-06-20 16:56:51,621 LOG Getting gch-messaging
2025-06-20 16:56:51,621 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-messaging.git  --depth 1 --origin upstream
2025-06-20 16:56:56,374 LOG Installing gch_messaging
2025-06-20 16:56:56,374 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_messaging 
2025-06-20 16:57:01,062 DEBUG bench build --app gch_messaging
2025-06-20 16:57:01,142 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_messaging
2025-06-20 16:57:02,612 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 16:57:02,759 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 16:57:02,759 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 16:58:21,228 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_messaging
2025-06-20 17:13:43,119 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-20 17:13:43,349 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-20 17:13:43,355 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-20 17:13:43,355 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-20 17:13:43,355 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-20 17:14:31,933 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-20 17:15:29,510 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git
2025-06-20 17:15:29,517 LOG Getting gch-queue
2025-06-20 17:15:29,517 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-queue.git  --depth 1 --origin upstream
2025-06-20 17:15:33,751 LOG Installing gch_queue
2025-06-20 17:15:33,752 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_queue 
2025-06-20 17:15:35,119 DEBUG bench build --app gch_queue
2025-06-20 17:15:35,212 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_queue
2025-06-20 17:15:46,183 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 17:15:46,351 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 17:15:46,351 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 17:15:58,566 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git
2025-06-20 17:15:58,573 LOG Getting gch-queue
2025-06-20 17:15:58,573 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-queue.git  --depth 1 --origin upstream
2025-06-20 17:16:02,602 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git executed with exit code 1
2025-06-20 17:16:17,905 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-20 17:16:28,643 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-20 17:16:28,864 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-20 17:16:28,882 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-20 17:16:28,898 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-20 17:16:28,901 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-20 17:16:40,682 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-20 17:17:03,573 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git
2025-06-20 17:17:06,345 INFO App moved from apps/gch-queue to archived/apps/gch-queue-2025-06-20
2025-06-20 17:17:06,352 LOG Getting gch-queue
2025-06-20 17:17:06,353 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-queue.git  --depth 1 --origin upstream
2025-06-20 17:17:10,493 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git executed with exit code 1
2025-06-20 17:17:46,533 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_queue **************:Gertrude-s-Children-s-Hospital/gch-queue.git
2025-06-20 17:17:55,962 INFO App moved from apps/gch-queue to archived/apps/gch-queue-2025-06-20_1
2025-06-20 17:17:55,968 LOG Getting gch-queue
2025-06-20 17:17:55,969 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-queue.git  --depth 1 --origin upstream
2025-06-20 17:18:00,279 LOG Installing gch_queue
2025-06-20 17:18:00,280 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_queue 
2025-06-20 17:18:01,512 DEBUG bench build --app gch_queue
2025-06-20 17:18:01,610 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_queue
2025-06-20 17:18:03,241 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-20 17:18:03,421 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-20 17:18:03,422 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-20 17:18:39,832 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_queue
2025-06-21 12:00:02,020 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-23 06:24:02,445 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 06:24:02,726 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 06:24:02,732 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 06:24:02,749 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 06:24:02,751 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 07:17:21,982 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_custom https://github.com/Gertrude-s-Children-s-Hospital/gch-custom.git
2025-06-23 07:17:21,991 LOG Getting gch-custom
2025-06-23 07:17:21,991 DEBUG cd ./apps && git clone https://github.com/Gertrude-s-Children-s-Hospital/gch-custom.git  --depth 1 --origin upstream
2025-06-23 07:17:26,566 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_custom https://github.com/Gertrude-s-Children-s-Hospital/gch-custom.git executed with exit code 1
2025-06-23 07:17:54,667 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_custom **************:Gertrude-s-Children-s-Hospital/gch-custom.git
2025-06-23 07:17:54,674 LOG Getting gch-custom
2025-06-23 07:17:54,674 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-custom.git  --depth 1 --origin upstream
2025-06-23 07:18:00,695 LOG Installing gch_custom
2025-06-23 07:18:00,696 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_custom 
2025-06-23 07:18:03,168 DEBUG bench build --app gch_custom
2025-06-23 07:18:03,288 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_custom
2025-06-23 07:18:13,423 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 07:18:13,613 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 07:18:13,613 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 07:18:49,086 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:19:03,663 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 07:19:03,938 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 07:19:03,947 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 07:19:03,953 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 07:19:03,967 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 07:19:14,137 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:19:50,918 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_custom
2025-06-23 07:24:23,203 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:24:37,616 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_custom
2025-06-23 07:27:09,859 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_custom --force
2025-06-23 07:27:58,484 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_custom --force
2025-06-23 07:31:14,446 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app payments
2025-06-23 07:31:15,235 LOG Getting payments
2025-06-23 07:31:15,235 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git  --depth 1 --origin upstream
2025-06-23 07:31:16,676 LOG Installing payments
2025-06-23 07:31:16,677 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/payments 
2025-06-23 07:31:25,078 DEBUG bench build --app payments
2025-06-23 07:31:25,182 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app payments
2025-06-23 07:31:27,274 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 07:31:27,466 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 07:31:27,466 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 07:31:43,170 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app hospitality
2025-06-23 07:31:43,805 LOG Getting hospitality
2025-06-23 07:31:43,805 DEBUG cd ./apps && git clone https://github.com/frappe/hospitality.git  --depth 1 --origin upstream
2025-06-23 07:31:45,221 LOG Installing hospitality
2025-06-23 07:31:45,221 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/hospitality 
2025-06-23 07:31:49,152 DEBUG bench build --app hospitality
2025-06-23 07:31:49,257 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app hospitality
2025-06-23 07:31:51,107 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 07:31:51,295 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 07:31:51,295 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 07:32:42,519 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app healthcare --branch version-15
2025-06-23 07:32:43,194 LOG Getting healthcare
2025-06-23 07:32:43,194 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare.git --branch version-15 --depth 1 --origin upstream
2025-06-23 07:32:44,966 LOG Installing healthcare
2025-06-23 07:32:44,966 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/healthcare 
2025-06-23 07:32:48,204 DEBUG bench build --app healthcare
2025-06-23 07:32:48,307 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app healthcare
2025-06-23 07:32:50,035 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 07:32:50,219 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 07:32:50,219 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 07:35:24,930 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app payments --force
2025-06-23 07:35:42,150 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app healthcare --force
2025-06-23 07:36:11,978 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app hospitality --force
2025-06-23 07:36:30,368 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:36:38,836 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 07:36:39,113 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 07:36:39,122 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 07:36:39,123 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 07:36:39,128 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 07:36:47,875 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:42:36,707 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_messaging
2025-06-23 07:43:25,680 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:49:42,316 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local mariadb
2025-06-23 07:50:14,434 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 07:58:08,398 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local list-apps
2025-06-23 07:58:32,080 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app healthcare
2025-06-23 07:58:34,887 INFO App moved from apps/healthcare to archived/apps/healthcare-2025-06-23
2025-06-23 07:58:34,896 LOG Getting healthcare
2025-06-23 07:58:34,896 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare.git --branch version-15 --depth 1 --origin upstream
2025-06-23 07:58:36,673 LOG Installing healthcare
2025-06-23 07:58:36,674 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/healthcare 
2025-06-23 07:58:39,456 DEBUG bench build --app healthcare
2025-06-23 07:58:39,569 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app healthcare
2025-06-23 07:58:46,622 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 07:58:46,857 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 07:58:46,857 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 07:58:59,777 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app healthcare
2025-06-23 07:59:34,823 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:04:41,109 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local console
2025-06-23 08:14:12,949 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:16:20,166 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:18:15,210 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_middleware **************:Gertrude-s-Children-s-Hospital/gch_middleware.git
2025-06-23 08:18:15,220 LOG Getting gch_middleware
2025-06-23 08:18:15,220 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch_middleware.git  --depth 1 --origin upstream
2025-06-23 08:18:19,480 LOG Installing gch_middleware
2025-06-23 08:18:19,480 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_middleware 
2025-06-23 08:18:37,736 DEBUG bench build --app gch_middleware
2025-06-23 08:18:37,890 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_middleware
2025-06-23 08:18:45,788 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:18:46,066 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:18:46,066 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 08:19:08,959 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_middleware
2025-06-23 08:19:16,731 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:19:25,495 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:19:25,838 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:19:25,857 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:19:25,862 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:19:25,871 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:19:31,486 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:21:42,634 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_insurance **************:Gertrude-s-Children-s-Hospital/gch_insurance.git
2025-06-23 08:21:42,652 LOG Getting gch_insurance
2025-06-23 08:21:42,652 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch_insurance.git  --depth 1 --origin upstream
2025-06-23 08:21:46,834 LOG Installing gch_insurance
2025-06-23 08:21:46,835 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_insurance 
2025-06-23 08:21:51,866 DEBUG bench build --app gch_insurance
2025-06-23 08:21:52,056 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_insurance
2025-06-23 08:21:54,431 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:21:54,684 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:21:54,684 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 08:22:06,666 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_insurance
2025-06-23 08:22:13,309 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:23:17,774 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:23:18,097 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:23:18,109 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:23:18,110 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:23:18,112 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:24:08,692 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theme **************:Gertrude-s-Children-s-Hospital/gch-theme.git
2025-06-23 08:24:08,703 LOG Getting gch-theme
2025-06-23 08:24:08,703 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theme.git  --depth 1 --origin upstream
2025-06-23 08:24:13,537 LOG Installing gch_theme
2025-06-23 08:24:13,537 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theme 
2025-06-23 08:24:18,616 DEBUG bench build --app gch_theme
2025-06-23 08:24:18,865 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theme
2025-06-23 08:24:21,417 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:24:21,647 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:24:21,647 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 08:24:36,790 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theme
2025-06-23 08:24:50,799 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:26:44,741 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:26:45,016 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:26:45,033 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:26:45,060 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:26:45,062 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:26:53,267 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:28:51,074 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_sentry **************:Gertrude-s-Children-s-Hospital/gch-sentry.git
2025-06-23 08:28:51,085 LOG Getting gch-sentry
2025-06-23 08:28:51,085 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-sentry.git  --depth 1 --origin upstream
2025-06-23 08:28:55,162 LOG Installing gch_sentry
2025-06-23 08:28:55,163 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_sentry 
2025-06-23 08:29:15,785 DEBUG cd /home/<USER>/job/gch_v2/gch-bench/apps/gch_sentry && yarn install --check-files
2025-06-23 08:29:18,679 DEBUG bench build --app gch_sentry
2025-06-23 08:29:18,895 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_sentry
2025-06-23 08:29:21,625 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:29:21,901 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:29:21,901 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 08:29:43,470 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_sentry
2025-06-23 08:29:55,869 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:29:56,151 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:29:56,152 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:29:56,166 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:29:56,195 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:30:01,175 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:32:02,577 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_purchases **************:Gertrude-s-Children-s-Hospital/gch-purchases.git
2025-06-23 08:32:02,588 LOG Getting gch-purchases
2025-06-23 08:32:02,588 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-purchases.git  --depth 1 --origin upstream
2025-06-23 08:32:06,872 LOG Installing gch_purchases
2025-06-23 08:32:06,873 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_purchases 
2025-06-23 08:32:12,060 DEBUG bench build --app gch_purchases
2025-06-23 08:32:12,215 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_purchases
2025-06-23 08:32:14,878 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:32:15,144 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:32:15,144 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 08:33:31,112 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_purchases
2025-06-23 08:33:49,410 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:33:49,721 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:33:49,734 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:33:49,754 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:33:49,759 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:33:56,571 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:36:05,852 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-06-23 08:36:18,453 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-website-cache
2025-06-23 08:36:44,543 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local reload-doc
2025-06-23 08:37:06,288 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --force
2025-06-23 08:37:47,122 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local console
2025-06-23 08:39:01,362 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-06-23 08:39:10,454 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-website-cache
2025-06-23 08:39:18,832 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --force
2025-06-23 08:40:31,268 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-06-23 08:40:35,661 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:40:35,995 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:40:35,995 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-06-23 08:41:01,923 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-06-23 08:41:02,527 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:41:02,793 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:41:02,793 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-06-23 08:41:42,413 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-06-23 08:41:42,941 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 08:41:43,224 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 08:41:43,225 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-06-23 08:45:44,511 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:45:44,780 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:45:44,787 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:45:44,791 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 08:45:44,809 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:46:37,713 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 08:54:01,050 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 08:54:01,332 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 08:54:01,336 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 08:54:01,338 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 08:54:01,339 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 10:11:22,106 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench list-apps
2025-06-23 10:13:31,729 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench list-apps
2025-06-23 10:13:54,315 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-23 10:14:07,102 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-23 10:49:04,960 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-23 12:00:01,300 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-23 13:19:58,938 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-23 13:20:40,949 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 13:20:41,238 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 13:20:41,252 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 13:20:41,260 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 13:20:41,271 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 13:24:09,820 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:24:09,828 LOG Getting gch-theatre
2025-06-23 13:24:09,828 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:24:16,763 LOG Installing gch_theatre
2025-06-23 13:24:16,763 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theatre 
2025-06-23 13:24:25,876 DEBUG bench build --app gch_theatre
2025-06-23 13:24:25,982 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theatre
2025-06-23 13:24:36,176 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:24:36,372 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:24:36,372 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:24:51,592 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 13:24:51,871 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 13:24:51,875 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 13:24:51,878 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 13:24:51,881 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 13:25:09,297 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:25:09,307 LOG Getting gch-theatre
2025-06-23 13:25:09,307 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:25:16,246 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git executed with exit code 1
2025-06-23 13:28:57,659 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:28:57,668 LOG Getting gch-theatre
2025-06-23 13:28:57,668 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:28:59,709 WARNING cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream executed with exit code 128
2025-06-23 13:28:59,710 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git executed with exit code 1
2025-06-23 13:31:32,545 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:31:32,552 LOG Getting gch-theatre
2025-06-23 13:31:32,553 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:31:36,794 LOG Installing gch_theatre
2025-06-23 13:31:36,795 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theatre 
2025-06-23 13:31:43,496 DEBUG bench build --app gch_theatre
2025-06-23 13:31:43,638 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theatre
2025-06-23 13:31:46,449 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:31:46,718 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:31:46,718 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:32:32,449 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-23 13:34:40,212 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_common <EMAIL>:Gertrude-s-Children-s-Hospital/gch-common.git
2025-06-23 13:34:40,218 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_common <EMAIL>:Gertrude-s-Children-s-Hospital/gch-common.git executed with exit code 1
2025-06-23 13:34:42,657 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 13:35:27,994 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_common **************:Gertrude-s-Children-s-Hospital/gch-common.git
2025-06-23 13:35:28,003 LOG Getting gch-common
2025-06-23 13:35:28,003 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-common.git  --depth 1 --origin upstream
2025-06-23 13:35:34,037 WARNING cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-common.git  --depth 1 --origin upstream executed with exit code 128
2025-06-23 13:35:34,038 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_common **************:Gertrude-s-Children-s-Hospital/gch-common.git executed with exit code 1
2025-06-23 13:36:17,717 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_common **************:Gertrude-s-Children-s-Hospital/gch-common.git
2025-06-23 13:36:17,726 LOG Getting gch-common
2025-06-23 13:36:17,726 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-common.git  --depth 1 --origin upstream
2025-06-23 13:36:22,453 LOG Installing gch_common
2025-06-23 13:36:22,453 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_common 
2025-06-23 13:36:26,183 DEBUG bench build --app gch_common
2025-06-23 13:36:26,288 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_common
2025-06-23 13:36:28,650 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:36:28,977 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:36:28,977 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:36:29,702 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 13:36:41,405 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_common
2025-06-23 13:37:07,039 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:37:07,047 LOG Getting gch-theatre
2025-06-23 13:37:07,048 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:37:13,086 WARNING cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream executed with exit code 128
2025-06-23 13:37:13,086 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git executed with exit code 1
2025-06-23 13:37:15,656 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 13:38:22,776 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:38:22,785 LOG Getting gch-theatre
2025-06-23 13:38:22,785 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:38:30,279 LOG Installing gch_theatre
2025-06-23 13:38:30,280 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theatre 
2025-06-23 13:38:34,078 DEBUG bench build --app gch_theatre
2025-06-23 13:38:34,183 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theatre
2025-06-23 13:38:36,558 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:38:36,838 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:38:36,838 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:38:51,434 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-23 13:39:42,301 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 13:39:51,414 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 13:39:51,689 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 13:39:51,698 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 13:39:51,725 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 13:39:51,726 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 13:39:59,229 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 13:41:22,612 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-23 13:42:27,360 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre --force
2025-06-23 13:44:40,007 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gech_inpatient **************:Gertrude-s-Children-s-Hospital/gch_inpatient.git
2025-06-23 13:44:40,029 LOG Getting gch_inpatient
2025-06-23 13:44:40,029 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch_inpatient.git  --depth 1 --origin upstream
2025-06-23 13:44:44,426 LOG Installing gch_inpatient
2025-06-23 13:44:44,427 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_inpatient 
2025-06-23 13:44:49,407 DEBUG bench build --app gch_inpatient
2025-06-23 13:44:49,598 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_inpatient
2025-06-23 13:44:52,410 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:44:52,679 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:44:52,679 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:44:55,197 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 13:45:08,554 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_inpatient
2025-06-23 13:45:28,860 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:45:28,869 LOG Getting gch-theatre
2025-06-23 13:45:28,869 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:45:33,638 LOG Installing gch_theatre
2025-06-23 13:45:33,639 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theatre 
2025-06-23 13:45:39,665 DEBUG bench build --app gch_theatre
2025-06-23 13:45:39,774 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theatre
2025-06-23 13:45:42,134 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:45:42,375 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:45:42,375 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:46:01,417 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-23 13:46:11,625 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre --force
2025-06-23 13:46:32,500 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre --force
2025-06-23 13:46:44,930 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 13:46:45,225 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 13:46:45,225 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 13:46:45,240 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 13:46:45,241 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 13:47:04,061 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre --force
2025-06-23 13:47:31,813 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 13:51:25,495 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_inpatient
2025-06-23 13:51:34,953 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre --force
2025-06-23 13:54:49,939 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gech_pccu **************:Gertrude-s-Children-s-Hospital/gch-pccu.git
2025-06-23 13:54:49,947 LOG Getting gch-pccu
2025-06-23 13:54:49,947 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-pccu.git  --depth 1 --origin upstream
2025-06-23 13:54:56,056 LOG Installing gch_pccu
2025-06-23 13:54:56,056 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_pccu 
2025-06-23 13:55:15,237 DEBUG bench build --app gch_pccu
2025-06-23 13:55:15,342 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_pccu
2025-06-23 13:55:17,818 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:55:18,096 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:55:18,096 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:55:41,366 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_pccu
2025-06-23 13:56:52,088 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_pacs **************:Gertrude-s-Children-s-Hospital/gch-pacs.git
2025-06-23 13:56:52,096 LOG Getting gch-pacs
2025-06-23 13:56:52,096 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-pacs.git  --depth 1 --origin upstream
2025-06-23 13:56:58,166 LOG Installing gch_pacs
2025-06-23 13:56:58,166 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_pacs 
2025-06-23 13:57:07,964 DEBUG bench build --app gch_pacs
2025-06-23 13:57:08,074 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_pacs
2025-06-23 13:57:10,504 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 13:57:10,796 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 13:57:10,796 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 13:57:28,293 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_pacs
2025-06-23 13:58:19,005 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 13:58:28,076 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-23 13:58:28,341 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-23 13:58:28,359 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-23 13:58:28,374 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-23 13:58:28,382 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-23 13:58:37,017 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 13:59:02,765 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_pacs **************:Gertrude-s-Children-s-Hospital/gch-pacs.git
2025-06-23 13:59:02,776 LOG Getting gch-pacs
2025-06-23 13:59:02,776 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-pacs.git  --depth 1 --origin upstream
2025-06-23 13:59:06,805 WARNING cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-pacs.git  --depth 1 --origin upstream executed with exit code 128
2025-06-23 13:59:06,806 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_pacs **************:Gertrude-s-Children-s-Hospital/gch-pacs.git executed with exit code 1
2025-06-23 13:59:07,177 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 13:59:33,641 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 13:59:33,650 LOG Getting gch-theatre
2025-06-23 13:59:33,651 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 13:59:39,696 WARNING cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream executed with exit code 128
2025-06-23 13:59:39,698 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git executed with exit code 1
2025-06-23 13:59:42,657 INFO A newer version of bench is available: 5.25.5 → 5.25.6
2025-06-23 14:00:28,974 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench get-app gch_theatre **************:Gertrude-s-Children-s-Hospital/gch-theatre.git
2025-06-23 14:00:28,983 LOG Getting gch-theatre
2025-06-23 14:00:28,983 DEBUG cd ./apps && <NAME_EMAIL>:Gertrude-s-Children-s-Hospital/gch-theatre.git  --depth 1 --origin upstream
2025-06-23 14:00:34,402 LOG Installing gch_theatre
2025-06-23 14:00:34,402 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && /home/<USER>/job/gch_v2/gch-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job/gch_v2/gch-bench/apps/gch_theatre 
2025-06-23 14:00:46,498 DEBUG bench build --app gch_theatre
2025-06-23 14:00:46,620 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench build --app gch_theatre
2025-06-23 14:00:49,283 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-23 14:00:49,543 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-23 14:00:49,543 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-23 14:01:25,675 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-23 14:01:39,598 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-23 14:05:36,630 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-23 14:17:17,024 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-24 09:42:22,225 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:42:22,526 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:42:22,551 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:42:22,553 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:42:22,560 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:43:56,884 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:43:57,169 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:43:57,180 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:43:57,193 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:43:57,215 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:45:50,027 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:45:50,378 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:45:50,386 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:45:50,404 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:45:50,409 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:48:56,116 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local set-password Administrator
2025-06-24 09:50:00,523 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:50:00,818 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:50:00,828 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:50:00,831 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:50:00,831 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:51:53,149 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local set-password <EMAIL>
2025-06-24 09:52:05,510 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:52:05,800 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:52:05,813 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:52:05,825 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:52:05,836 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:53:54,885 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-24 09:54:20,109 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 09:55:02,639 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 09:55:02,949 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 09:55:02,952 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 09:55:02,957 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 09:55:02,965 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 09:55:08,800 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 10:01:25,515 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 10:04:06,332 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 10:04:06,612 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 10:04:06,645 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 10:04:06,656 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 10:04:06,658 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 10:42:50,200 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 11:08:32,359 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 11:24:08,297 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local reinstall
2025-06-24 11:26:52,456 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app healthcare
2025-06-24 11:27:24,832 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app hospitality
2025-06-24 11:27:38,467 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app payments
2025-06-24 11:30:38,581 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_middleware
2025-06-24 11:30:48,837 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_messaging
2025-06-24 11:30:58,540 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_custom
2025-06-24 11:32:43,689 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_inpatient
2025-06-24 11:32:58,793 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_insurance
2025-06-24 11:33:11,212 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theme
2025-06-24 11:33:29,452 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_common
2025-06-24 11:33:39,350 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_queue
2025-06-24 11:33:49,969 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_theatre
2025-06-24 11:34:11,291 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_middleware
2025-06-24 11:34:18,682 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_pacs
2025-06-24 11:34:46,402 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_pccu
2025-06-24 11:35:03,840 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_sentry
2025-06-24 11:35:20,485 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local list-apps
2025-06-24 11:35:33,553 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 11:38:02,228 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local install-app gch_purchases
2025-06-24 11:38:16,390 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-24 11:47:31,592 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench pip install pygrowup
2025-06-24 11:47:51,766 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 11:47:52,057 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 11:47:52,083 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 11:47:52,107 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-24 11:47:52,110 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 12:00:01,773 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-24 12:03:11,366 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench version
2025-06-24 13:55:42,561 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-24 13:55:42,812 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-24 13:55:42,834 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-24 13:55:42,853 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-24 13:55:42,863 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 07:30:08,572 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 07:30:09,181 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 07:30:09,182 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 07:30:09,206 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 07:30:09,242 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 10:11:07,719 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench -site gch.local build
2025-06-25 10:11:47,410 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local build
2025-06-25 10:12:42,278 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:12:42,859 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 10:12:42,876 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 10:12:42,877 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 10:12:42,930 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 10:14:22,811 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-06-25 10:14:35,821 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-website-cache
2025-06-25 10:14:50,995 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench clear-cache
2025-06-25 10:15:08,805 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:22:05,929 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:22:06,566 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 10:22:06,566 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 10:22:06,570 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 10:22:06,636 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 10:30:54,305 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:30:54,882 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 10:30:54,883 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 10:30:54,892 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 10:30:54,948 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 10:32:30,955 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cahce
2025-06-25 10:33:15,621 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-06-25 10:33:28,160 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local website-clear-cache
2025-06-25 10:33:57,070 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-website-cache
2025-06-25 10:34:19,275 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:34:19,828 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 10:34:19,830 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 10:34:19,846 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 10:34:19,887 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 10:50:26,093 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local build
2025-06-25 10:54:48,658 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 10:54:49,244 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 10:54:49,273 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 10:54:49,276 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 10:54:49,309 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 12:00:01,921 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-25 13:39:23,375 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 13:39:24,054 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 13:39:24,068 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 13:39:24,070 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 13:39:24,175 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 14:12:42,899 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 14:12:43,489 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 14:12:43,507 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 14:12:43,522 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 14:12:43,614 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 14:16:39,736 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gech.local migrate
2025-06-25 14:16:53,662 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-25 14:17:02,977 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-25 14:17:03,638 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-25 14:17:03,644 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-25 14:17:03,651 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-25 14:17:03,712 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-25 14:19:46,569 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local migrate
2025-06-26 09:45:01,270 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site clear-cache
2025-06-26 09:45:14,248 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-06-26 09:45:24,556 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-website-cache
2025-06-26 09:45:33,061 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 09:45:33,644 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 09:45:33,652 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 09:45:33,671 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 09:45:33,737 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 10:22:37,712 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 10:22:38,361 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 10:22:38,366 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 10:22:38,372 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 10:22:38,457 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 10:48:30,124 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local console
2025-06-26 11:11:24,429 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-06-26 11:11:41,956 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-06-26 11:11:42,465 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-26 11:11:42,466 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-06-26 11:11:43,339 INFO A newer version of bench is available: 5.25.5 → 5.25.7
2025-06-26 11:11:59,757 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 11:12:00,370 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 11:12:00,385 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 11:12:00,389 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 11:12:00,488 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 11:15:58,331 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 11:15:59,045 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 11:15:59,058 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 11:15:59,074 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 11:15:59,148 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 11:32:19,447 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local console
2025-06-26 11:39:19,319 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local console
2025-06-26 11:50:33,668 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 11:50:34,342 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 11:50:34,346 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 11:50:34,369 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 11:50:34,429 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 12:00:02,338 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-26 12:25:16,219 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 12:25:16,974 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 12:25:17,062 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 12:25:17,087 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 12:25:17,141 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 14:26:58,054 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench show-scheduler-status
2025-06-26 14:29:11,333 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local show-scheduler-status
2025-06-26 14:30:12,030 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local execute frappe.core.doctype.scheduled_job_type.scheduled_job_type.is_scheduler_enabled
2025-06-26 16:49:33,645 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 16:49:34,394 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 16:49:34,438 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 16:49:34,449 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 16:49:34,540 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-26 17:08:56,926 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-06-26 17:08:57,655 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-06-26 17:08:57,667 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-06-26 17:08:57,676 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-06-26 17:08:57,747 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-06-27 12:00:01,752 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-06-30 18:00:51,659 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-05 00:00:02,093 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-06 00:00:01,998 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-07 00:00:02,071 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-07 04:38:44,566 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-07 04:38:44,838 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-07 04:38:44,838 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-07 04:38:44,840 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-07 04:38:44,910 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-07 12:00:02,108 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-07 14:38:06,549 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-07 14:38:06,891 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-07 14:38:06,916 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-07 14:38:06,921 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-07 14:38:06,969 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-07 14:44:05,355 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-07 14:44:05,632 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-07 14:44:05,634 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-07 14:44:05,637 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-07 14:44:05,718 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-08 00:00:01,634 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-08 09:00:19,755 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-07-08 09:00:20,615 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-07-08 09:00:31,911 DEBUG cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe:
2025-07-08 09:00:32,178 WARNING cd /home/<USER>/job/gch_v2/gch-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-08 09:00:32,178 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-07-08 09:00:32,870 INFO A newer version of bench is available: 5.25.5 → 5.25.9
2025-07-08 09:32:22,188 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench restart
2025-07-08 09:34:11,918 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-08 09:34:50,018 WARNING /home/<USER>/job/gch_v2/gch-env/bin/bench restart executed with exit code 1
2025-07-08 09:34:52,523 INFO A newer version of bench is available: 5.25.5 → 5.25.9
2025-07-08 10:34:40,435 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench clear-cache
2025-07-08 10:43:41,291 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-08 10:43:41,548 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-08 10:43:41,552 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-08 10:43:41,553 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-08 10:43:41,557 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-08 12:00:01,735 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-09 00:00:01,498 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-09 07:20:38,756 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-09 07:20:38,981 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-09 07:20:38,987 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-09 07:20:38,996 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-09 07:20:38,996 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-09 07:46:31,814 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-09 12:00:01,653 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-10 12:00:01,667 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-11 08:33:33,518 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-11 08:33:34,190 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-11 08:33:34,282 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-11 08:33:34,282 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-11 08:33:34,345 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-11 12:00:01,459 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-12 12:00:01,579 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-13 00:00:01,953 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-14 05:29:52,464 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 05:29:52,714 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 05:29:52,715 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-14 05:29:52,728 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 05:29:52,735 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 06:58:13,791 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 06:58:13,995 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-14 06:58:14,036 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 06:58:14,038 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 06:58:14,042 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 08:57:28,644 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 08:57:28,896 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 08:57:28,898 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 08:57:28,904 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 08:57:28,912 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-14 12:00:01,812 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-14 12:33:06,218 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local clear-cache
2025-07-14 12:33:32,796 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 12:33:33,134 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-14 12:33:33,138 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 12:33:33,150 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 12:33:33,155 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 15:23:03,946 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 15:23:04,258 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 15:23:04,273 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 15:23:04,274 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 15:23:04,277 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-14 16:18:05,183 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-14 16:18:05,502 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-14 16:18:05,508 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-14 16:18:05,518 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-14 16:18:05,521 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 06:56:57,015 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 06:57:11,826 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 07:02:20,736 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 07:02:21,044 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 07:02:21,050 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 07:02:21,054 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 07:02:21,059 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 08:21:12,637 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 08:21:12,935 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 08:21:12,942 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 08:21:12,943 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 08:21:12,951 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 08:41:56,237 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 08:41:56,544 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 08:41:56,549 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 08:41:56,551 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 08:41:56,552 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 09:35:23,937 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 09:35:24,240 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 09:35:24,246 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 09:35:24,250 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 09:35:24,250 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 10:16:06,443 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 10:16:06,791 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 10:16:06,796 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 10:16:06,798 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 10:16:06,810 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 10:38:52,311 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-15 10:38:52,620 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-15 10:38:52,630 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-15 10:38:52,631 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-15 10:38:52,632 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-15 12:00:01,320 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-16 06:41:34,738 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-16 06:41:35,318 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-16 06:41:35,332 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-16 06:41:35,346 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-16 06:41:35,347 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-16 12:00:01,554 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-16 12:00:49,447 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-16 12:00:49,956 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-16 12:00:49,962 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-16 12:00:49,967 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-16 12:00:49,989 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 06:23:49,161 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 06:23:49,419 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 06:23:49,422 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 06:23:49,425 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 06:23:49,434 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 06:53:50,970 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 06:53:51,208 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 06:53:51,217 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 06:53:51,217 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 06:53:51,224 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 06:55:25,312 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 06:55:25,540 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 06:55:25,542 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 06:55:25,545 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 06:55:25,549 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 07:07:44,756 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 07:07:45,032 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 07:07:45,047 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 07:07:45,049 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 07:07:45,049 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 07:23:52,074 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 07:23:52,293 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 07:23:52,323 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 07:23:52,332 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 07:23:52,332 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 07:54:56,363 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 07:54:56,598 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 07:54:56,617 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 07:54:56,621 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 07:54:56,621 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-17 12:00:01,671 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-17 14:26:35,999 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench start
2025-07-17 14:26:36,508 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench schedule
2025-07-17 14:26:36,530 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench serve --port 8000
2025-07-17 14:26:36,548 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench worker
2025-07-17 14:26:36,564 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench watch
2025-07-18 06:00:01,746 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --verbose --site all backup
2025-07-18 07:31:16,481 INFO /home/<USER>/job/gch_v2/gch-env/bin/bench --site gch.local mariadb
