{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:09.313532", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "_followup_actions", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 173, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "left_ear_results", "is_system_generated": 0, "is_virtual": 0, "label": " Followup Actions", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 11:49:11.331882", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-_followup_actions", "no_copy": 0, "non_negative": 0, "options": "\nRetest\nRefer to Audiology\nPass\nProceed", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:09:01.361640", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "airway_and_breathing", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 114, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "emergency_flagging", "is_system_generated": 0, "is_virtual": 0, "label": "Airway and Breathing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:09:01.361640", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-airway_and_breathing", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 20:01:17.416126", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "allergies", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 93, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_assessment", "is_system_generated": 0, "is_virtual": 0, "label": "Allergies", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-25 12:59:09.878918", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-allergies", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-02 08:14:04.295142", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Doctor\") || frappe.user.has_role(\"System Manager\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "allergies_doctor_view", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 239, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pharmacy_dispensement_form", "is_system_generated": 0, "is_virtual": 0, "label": "Allergies Doctor View", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-02 08:14:04.295142", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-allergies_doctor_view", "no_copy": 0, "non_negative": 0, "options": "<div class=\"form-row mb-3\">\n{% if doc.drug_allergy %}\n    <div class=\"col-md-4 \">\n        <label for=\"patientDrugAllergy\"><strong>Drug Allergies ({{doc.drug_allergy.length}})</strong></label>\n        {% for allergy in doc.drug_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientDrugAllergy\">{{allergy.drug_allergy}}</span>\n        {% endfor %}\n    </div>\n{% endif %}\n{% if doc.food_allergy %}\n    <div class=\"col-md-4 \">\n        <label for=\"patientFoodAllergy\"><strong>Food Allergies ({{doc.food_allergy.length}})</strong></label>\n        {% for allergy in doc.food_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientFoodAllergy\">{{allergy.food_allergy}}</span>\n        {% endfor %}\n    </div>\n{% endif %}\n{% if doc.other_allergy %}\n    <div class=\"col-md-4 \">\n        <label for=\"patientFoodAllergy\"><strong>Other Allergies ({{doc.other_allergy.length}})</strong></label>\n        {% for allergy in doc.other_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientFoodAllergy\">{{allergy.other_allergy}}</span>\n        {% endfor %}\n    </div>\n{% endif %}\n</div>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 16:23:42.529878", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "allergy_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 98, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "other_allergy", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-20 21:05:56.751306", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-allergy_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:31:42.137839", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "anaphylaxis", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 130, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "emergency_other", "is_system_generated": 0, "is_virtual": 0, "label": "Anaphylaxis <PERSON> or Tongue", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-21 21:35:06.791654", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-anaphylaxis", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:25:04.334409", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "anthropometry", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 63, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vital_signs_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Anthropometry", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-29 10:27:46.591058", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-anthropometry", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:31:20.125196", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "anthropometry_break", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 74, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bmi_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-20 12:35:50.907229", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-anthropometry_break", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-06-16 11:10:50.469932", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "anthropometry_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 64, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "anthropometry", "is_system_generated": 0, "is_virtual": 0, "label": "Anthropometry History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-06-02 16:17:59.720492", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-anthropometry_table", "no_copy": 0, "non_negative": 0, "options": "Anthropometry Details GCH", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:35:10.167674", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "artificial_airway", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 134, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "neck_stiffness", "is_system_generated": 0, "is_virtual": 0, "label": "Artificial Airway", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:35:10.167674", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-artificial_airway", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 17:04:08.827133", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "assessment_tools", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 107, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "routine_nutritional_counselling_not_done_at_6_months", "is_system_generated": 0, "is_virtual": 0, "label": "Assessment Tools", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:46:56.448604", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-assessment_tools", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-04-04 09:41:19.157508", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "auth_token", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 294, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "integrator", "is_system_generated": 0, "is_virtual": 0, "label": "auth_token", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-04-03 22:01:55.677174", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-auth_token", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:26:06.756118", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "avpu_is_p_or_u_or_convulsion", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 125, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "coma_convulsing_confusions", "is_system_generated": 0, "is_virtual": 0, "label": "AVPU is P or U or Convulsion", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:26:06.756118", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-avpu_is_p_or_u_or_convulsion", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:33:42.942165", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bmi", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 75, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "anthropometry_break", "is_system_generated": 0, "is_virtual": 0, "label": "BMI", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-28 04:55:15.700254", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-bmi", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "1", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-05 06:52:56.800736", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bmi_for_age", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 76, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bmi", "is_system_generated": 0, "is_virtual": 0, "label": "BMI for Age Percentile", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-05 08:43:12.276001", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-bmi_for_age", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-29 14:15:04.569194", "default": "", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bmi_for_age_chart", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 73, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "z_score_chart", "is_system_generated": 0, "is_virtual": 0, "label": "BMI for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-24 01:24:17.351072", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-bmi_for_age_chart", "no_copy": 0, "non_negative": 0, "options": "<canvas id=\"bmiAgeChart\" width=\"400\" height=\"400\"></canvas>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:19:27.990580", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "branch", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 28, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "company", "is_system_generated": 0, "is_virtual": 0, "label": "Branch", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-11-12 09:50:28.161145", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-branch", "no_copy": 0, "non_negative": 0, "options": "Branch", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 1, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:29:56.136433", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bsa", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 71, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "malnutrition_level", "is_system_generated": 0, "is_virtual": 0, "label": "BSA", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-28 04:55:34.369870", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-bsa", "no_copy": 0, "non_negative": 1, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:33:00.898131", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bulging_anterior_fontanelle", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 131, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "anaphylaxis", "is_system_generated": 0, "is_virtual": 0, "label": "Bulging anterior fontanelle", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-10-21 14:09:52.704826", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-bulging_anterior_fontanelle", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:22:13.659158", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "capillary_return_time_gt_3_sec", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 122, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "weak_or_fast_pulse_gt_160", "is_system_generated": 0, "is_virtual": 0, "label": "Capillary Return time Greater than 3 Sec", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:34:08.381710", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-capillary_return_time_gt_3_sec", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:12:21.277394", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "central_cyanosis_or_spo2", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 117, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "obstructed_breathing", "is_system_generated": 0, "is_virtual": 0, "label": "Central Cyanosis or SPO2 less than 90", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-21 21:33:48.083391", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-central_cyanosis_or_spo2", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-25 03:30:11.681832", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "change_date_of_birth", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 14, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "date_of_birth", "is_system_generated": 0, "is_virtual": 0, "label": "Change Date of Birth", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-25 03:40:07.372017", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-change_date_of_birth", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:55:59.920046", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "chest_wall_indrawing", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 141, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "grunting", "is_system_generated": 0, "is_virtual": 0, "label": "Chest Wall Indrawing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:55:59.920046", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-chest_wall_indrawing", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-10-29 12:18:46.926814", "default": null, "depends_on": "eval:(doc.clinic == 'Wellbaby - GCH' || doc.clinic == 'Wellbaby - W') && doc.__islocal != 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "child_fit_for_vaccination", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 160, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "immunization", "is_system_generated": 0, "is_virtual": 0, "label": "Child fit for Vaccination", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-25 13:13:30.584969", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-child_fit_for_vaccination", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:06.288783", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "child_milestone", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 181, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "development_hearing", "is_system_generated": 0, "is_virtual": 0, "label": "Child Milestones", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 16:57:17.980227", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-child_milestone", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nDelayed", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:15:17.127056", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "circulation", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 119, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "severe_respiratory_distress", "is_system_generated": 0, "is_virtual": 0, "label": "Circulation", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:15:17.127056", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-circulation", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-11 18:23:39.786196", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "clinic", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 34, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "practitioner_name", "is_system_generated": 0, "is_virtual": 0, "label": "Clinic", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:frappe.user.has_role(\"GCH-Reception\") && doc.workflow_state == \"Pending Triage\"", "modified": "2021-08-11 18:23:39.786196", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-clinic", "no_copy": 0, "non_negative": 0, "options": "Healthcare Service Unit", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-04-18 07:32:31.985928", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "closing_date_and_time", "fieldtype": "Datetime", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 49, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "revisit_reason_dur", "is_system_generated": 0, "is_virtual": 0, "label": "Closing Date and Time", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-04-18 07:38:25.240703", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-closing_date_and_time", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:16:01.502395", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "cold_hands_with", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 120, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "circulation", "is_system_generated": 0, "is_virtual": 0, "label": "Cold Hands With", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:16:01.502395", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-cold_hands_with", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-01 08:40:45.291857", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_153", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 277, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wellbaby_nutrition_counselling", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 12:30:43.540021", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-column_break_153", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:24:01.031986", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "coma_convulsing_confusions", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 124, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pulse_rate_lt_60_per_min", "is_system_generated": 0, "is_virtual": 0, "label": "Coma or Convulsing or Confusions", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:37:13.286604", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-coma_convulsing_confusions", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 08:10:32.769621", "default": null, "depends_on": "eval:doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "consultant_comments", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 251, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "enter_referral_reason", "is_system_generated": 0, "is_virtual": 0, "label": "Consultant comments", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-09-27 08:10:32.769621", "modified_by": "<EMAIL>", "module": null, "name": "Patient <PERSON>unter-consultant_comments", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:03.965444", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "cord_stump_healing", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 176, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "growth_monitoring", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-07 13:42:30.511530", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-cord_stump_healing", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nAbnormal", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:49.587656", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_bpdiastolic", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 60, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_bpsystolic", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-03 08:15:49.587656", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_bpdiastolic", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:48.858815", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_bpsystolic", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 59, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_oxygen_saturation", "is_system_generated": 0, "is_virtual": 0, "label": "critical_vital_bpsystolic", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-03 08:15:48.858815", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_bpsystolic", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:46.566957", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_heart_rate", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 56, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_temperature", "is_system_generated": 0, "is_virtual": 0, "label": "critical_vital_heart_rate", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-08-03 08:15:46.566957", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_heart_rate", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:48.100403", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_oxygen_saturation", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 58, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_respiratory_rate", "is_system_generated": 0, "is_virtual": 0, "label": "critical_vital_oxygen_saturation", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-03 08:15:48.100403", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_oxygen_saturation", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:47.376568", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_respiratory_rate", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 57, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_heart_rate", "is_system_generated": 0, "is_virtual": 0, "label": "critical_vital_respiratory_rate", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-03 08:15:47.376568", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_respiratory_rate", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-07-27 19:11:17.011447", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_signs", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 54, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vital_signs", "is_system_generated": 0, "is_virtual": 0, "label": "Critical Vital Signs", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-06-27 07:11:17.011447", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_signs", "no_copy": 0, "non_negative": 0, "options": "{% if doc.critical_vital_respiratory_rate || doc.critical_vital_temperature || doc.critical_vital_heart_rate || doc.critical_vital_bpdiastolic || doc.critical_vital_bpsystolic || doc.critical_vital_oxygen_saturation %}\n<h6 style=\"color: red;\">CRITICAL VITALS</h6>\n{% endif %}\n\n<div class=\"form-row mb-3\">\n    {% if doc.critical_vital_temperature %}\n    <div class=\"col-md-2 \">\n             Temp: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_temperature }}</span>\n    </div>\n    {% endif %}\n    {% if doc.critical_vital_respiratory_rate %}\n    <div class=\"col-md-2 \">\n             Respiratory Rate: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_respiratory_rate }}</span>\n    </div>\n    {% endif %}\n    {% if doc.critical_vital_heart_rate %}\n    <div class=\"col-md-2 \">\n             Heart Rate: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_heart_rate }}</span>\n    </div>\n    {% endif %}\n    {% if doc.critical_vital_bpdiastolic %}\n    <div class=\"col-md-2 \">\n             BP Diastolic: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_bpdiastolic }}</span>\n    </div>\n    {% endif %}\n    {% if doc.critical_vital_bpsystolic %}\n    <div class=\"col-md-2 \">\n             BP Systolic: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_bpsystolic }}</span>\n    </div>\n    {% endif %}\n    {% if doc.critical_vital_oxygen_saturation %}\n    <div class=\"col-md-2 \">\n             Oxygen Saturation: <span style=\"color: red; font-weight: bold;\">{{ doc.critical_vital_oxygen_saturation }}</span>\n    </div>\n    {% endif %}\n    </div>\n</div>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 1, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-03 08:15:45.626938", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "critical_vital_temperature", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 55, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_signs", "is_system_generated": 0, "is_virtual": 0, "label": "critical_vital_temperature", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-03 08:15:45.626938", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-critical_vital_temperature", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:21:29.639455", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_assessment", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 92, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "triage_medication", "is_system_generated": 0, "is_virtual": 0, "label": "Assessment", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:21:29.639455", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_assessment", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-25 11:18:31.619017", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_fcqkq", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 101, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "food_allergy", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-25 11:18:31.619017", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_column_break_fcqkq", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 11:35:41.071533", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_column_break_ju2dv", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 94, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "allergies", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 11:35:41.071533", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_column_break_ju2dv", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:29:07.123703", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_diagnosis", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 205, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_physical_examination", "is_system_generated": 0, "is_virtual": 0, "label": "Diagnosis", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:29:07.123703", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_diagnosis", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:22:34.393068", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_flagging", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 112, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_assessment_table", "is_system_generated": 0, "is_virtual": 0, "label": "Flagging", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:22:34.393068", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_flagging", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:27:37.018174", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_history", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 196, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "queue_group", "is_system_generated": 0, "is_virtual": 0, "label": "History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:27:37.018174", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_history", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:19:18.227703", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_nursing_notes", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 85, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "height_length_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Nursing Notes", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:19:18.227703", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_nursing_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:24:58.739845", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_payment", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 182, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "child_milestone", "is_system_generated": 0, "is_virtual": 0, "label": "Payment", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:24:58.739845", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_payment", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:27:35.885188", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_physical_examination", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 199, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_history", "is_system_generated": 0, "is_virtual": 0, "label": "Physical Examination", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:27:35.885188", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_physical_examination", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:32:35.092818", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_plan_of_action", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 212, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_nursing_diagnosis", "is_system_generated": 0, "is_virtual": 0, "label": "Plan of Action", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:32:35.092818", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_plan_of_action", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 14:20:34.736301", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_prescription", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 227, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "procedure_prescription", "is_system_generated": 0, "is_virtual": 0, "label": "Prescription", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 14:20:34.736301", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_prescription", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:37:06.681667", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_referrals", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 241, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "prescription_table", "is_system_generated": 0, "is_virtual": 0, "label": "Referrals", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:37:06.681667", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_referrals", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 11:35:40.556507", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_section_break_010h4", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 41, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "submit_orders_on_save", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 11:35:40.556507", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_section_break_010h4", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-07 15:40:31.204799", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_section_break_rit9l", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 302, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_details_tab", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-07 15:40:31.204799", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_section_break_rit9l", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 11:35:41.996488", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_section_break_wewje", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 298, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "print_format_selector", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 11:35:41.996488", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_section_break_wewje", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:32:35.445564", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_services", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 220, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "section_break_qjjp", "is_system_generated": 0, "is_virtual": 0, "label": "Services", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:32:35.445564", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_services", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-07 16:12:46.801385", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_tab_10", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 255, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "therapies", "is_system_generated": 0, "is_virtual": 0, "label": "Flagging", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-07 16:12:46.801385", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_tab_10", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-18 16:43:04.059513", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_triage", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 52, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reason_for_visit", "is_system_generated": 0, "is_virtual": 0, "label": "Triage", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-18 16:43:04.059513", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_triage", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-19 09:23:51.462144", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_wellbaby", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 158, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "unable_to_drink_or_vomits_anything", "is_system_generated": 0, "is_virtual": 0, "label": "Wellbaby", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-19 09:23:51.462144", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-custom_wellbaby", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-02-08 07:56:01.637882", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "data_fetched_from_prev_enc", "fieldtype": "Check", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 292, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_with_doctor", "is_system_generated": 0, "is_virtual": 0, "label": "data_fetched_from_prev_enc", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-17 14:05:22.460906", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-data_fetched_from_prev_enc", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:46.907083", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "date_and_time", "fieldtype": "Datetime", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 285, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_reopen_details", "is_system_generated": 0, "is_virtual": 0, "label": "Date and Time", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:49:35.968113", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-date_and_time", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-07-27 04:32:47.957651", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "patient.dob", "fetch_if_empty": 0, "fieldname": "date_of_birth", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 13, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_sex", "is_system_generated": 0, "is_virtual": 0, "label": "Date Of Birth", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-25 04:03:12.436559", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-date_of_birth", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": "", "columns": 0, "creation": "2022-03-28 13:42:01.903034", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "default_insurance", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 186, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "mode_of_payment", "is_system_generated": 0, "is_virtual": 0, "label": "default_insurance", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 17:17:16.629361", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-default_insurance", "no_copy": 0, "non_negative": 0, "options": "Insurance Category", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 17:33:45.152583", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "development_eyesight", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 178, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "eyesight_and_hearing", "is_system_generated": 0, "is_virtual": 0, "label": "Eyesight", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-23 17:34:15.615507", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-development_eyesight", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nAbnormal", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 17:33:12.216177", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "development_hearing", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 180, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "development_monitoring_column_break", "is_system_generated": 0, "is_virtual": 0, "label": "Hearing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 17:38:04.098315", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-development_hearing", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nAbnormal", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-11 12:58:55.701675", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "development_monitoring_column_break", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 179, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "development_eyesight", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:59:03.152981", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-development_monitoring_column_break", "no_copy": 0, "non_negative": 0, "options": "\n", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-05-18 11:46:16.327694", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "diag_field", "fieldtype": "HTML", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 233, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_info", "is_system_generated": 0, "is_virtual": 0, "label": "Diag Field", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-06-27 07:02:04.200145", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diag_field", "no_copy": 0, "non_negative": 0, "options": "<label><strong>Encounter Diagnosis</strong></label>\n\n{% for diagnosis in doc.diagnosis_table %}\n     \n  \n      <span>{{diagnosis.description}}</span>\n   \n<hr>\n{% endfor %}\n  ", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-23 12:46:41.356515", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "diagnosis_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 209, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "medical_diagnosis", "is_system_generated": 0, "is_virtual": 0, "label": "Diagnosis Table", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 12:48:12.702249", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diagnosis_table", "no_copy": 0, "non_negative": 0, "options": "Codification Table", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:28:26.028935", "default": "", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "diarrhea", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 127, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "emergency_cbreak", "is_system_generated": 0, "is_virtual": 0, "label": "Diarrhea", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:28:26.028935", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diarrhea", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:29:31.351449", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "diarrhea_with_sunken_eyes", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 128, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "diarrhea", "is_system_generated": 0, "is_virtual": 0, "label": "Diarrhea with Sunken eyes return of pinch Greater or equal to 2 sec", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-22 21:31:18.711528", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diarrhea_with_sunken_eyes", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-08-25 03:18:12.659060", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "dob", "fieldtype": "Date", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 15, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "change_date_of_birth", "is_system_generated": 0, "is_virtual": 0, "label": "dob", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-08-25 03:51:48.638670", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-dob", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-11-29 16:59:35.649266", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "done_by", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 174, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "_followup_actions", "is_system_generated": 0, "is_virtual": 0, "label": "Done by", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-29 16:59:35.649266", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-done_by", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:58:04.053795", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "drooling", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 144, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "stridor", "is_system_generated": 0, "is_virtual": 0, "label": "Drooling", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:58:04.053795", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-drooling", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 14:32:50.118177", "default": null, "depends_on": "eval:(doc.has_no_drug_allergy!=true) ", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "drug_allergy", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 96, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "has_no_drug_allergy", "is_system_generated": 0, "is_virtual": 0, "label": "Drug Allergy", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2023-02-09 20:14:11.351713", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-drug_allergy", "no_copy": 0, "non_negative": 0, "options": "Patient Drug Allergy", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:27:44.133530", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "emergency_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 126, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "avpu_is_p_or_u_or_convulsion", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:33:28.961213", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-emergency_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:07:45.808778", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "emergency_flagging", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 113, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_flagging", "is_system_generated": 0, "is_virtual": 0, "label": "Emergency Flagging", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-23 16:09:10.149358", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-emergency_flagging", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-22 21:36:42.492949", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "emergency_other", "fieldtype": "Heading", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 129, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "diarrhea_with_sunken_eyes", "is_system_generated": 0, "is_virtual": 0, "label": "Other", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-22 21:38:41.276984", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-emergency_other", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-03-20 10:15:15.305862", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "encounter_assessment_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 111, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_encounter_assessment", "is_system_generated": 0, "is_virtual": 0, "label": "Encounter Assessment Table", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-04-02 12:32:12.576641", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_assessment_table", "no_copy": 0, "non_negative": 0, "options": "Encounter Assessments Table", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-04-18 07:41:33.992824", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "encounter_closed_by", "fieldtype": "Link", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 50, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "closing_date_and_time", "is_system_generated": 0, "is_virtual": 0, "label": "Encounter Closed By", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-04-18 07:41:33.992824", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_closed_by", "no_copy": 0, "non_negative": 0, "options": "User", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 15:12:53.793001", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "encounter_history", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 197, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_history", "is_system_generated": 0, "is_virtual": 0, "label": "History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-07 14:24:32.491194", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_history", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 3, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": "eval:(doc.__islocal)", "columns": 0, "creation": "2021-07-20 12:03:31.600516", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Audiology\") || frappe.user.has_role(\"System Manager\") || frappe.user.has_role(\"GCH-Credit-Control\") || frappe.user.has_role(\"GCH-Endocrinology\") || frappe.user.has_role(\"GCH-Pharmacy\") || frappe.user.has_role(\"GCH-Reception\") || frappe.user.has_role(\"LabTest Approver\") || frappe.user.has_role(\"Nursing User\") || frappe.user.has_role(\"GCH-COO\") || frappe.user.has_role(\"GCH-Dental\") || frappe.user.has_role(\"GCH-Gastroenterology\") || frappe.user.has_role(\"GCH-Pharmacy Store\") || frappe.user.has_role(\"GCH-LabTechnician\") || frappe.user.has_role(\"GCH-TriageNurse\") || frappe.user.has_role(\"GCH-Credit Control\") || frappe.user.has_role(\"GCH-Hematology\") || frappe.user.has_role(\"GCH-Nurse\") || frappe.user.has_role(\"GCH-Ophthalmology\") || frappe.user.has_role(\"GCH-Radiology\") | frappe.user.has_role(\"GCH-Wellbaby Nurse\")\n", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "encounter_information", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 20, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_isolation_patient", "is_system_generated": 0, "is_virtual": 0, "label": "Encounter Information", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 17:36:12.578984", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_information", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 1, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:08:16.442443", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "encounter_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 21, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_information", "is_system_generated": 0, "is_virtual": 0, "label": "Encounter Number", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-08-13 06:25:47.179239", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_number", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:45.986404", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Reception\") && cur_frm.doc.workflow_state == \"Encounter Closed\"", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "encounter_reopen_details", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 284, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "workflow_state", "is_system_generated": 0, "is_virtual": 0, "label": "Encounter Reopen Details", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:43:01.452211", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_reopen_details", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-31 04:25:05.843075", "default": null, "depends_on": "eval:doc.reason_for_referral == 'Other' && doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "enter_referral_reason", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 250, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reason_for_referral", "is_system_generated": 0, "is_virtual": 0, "label": "Enter other referral reason", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.reason_for_referral == 'Other' && doc.is_referral == 1", "modified": "2023-01-31 04:25:05.843075", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-enter_referral_reason", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-01 08:40:33.858044", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "eyesight_and_hearing", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 177, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "cord_stump_healing", "is_system_generated": 0, "is_virtual": 0, "label": "Eyesight and Hearing", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-25 12:20:33.392060", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-eyesight_and_hearing", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nAbnormal", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:42:59.587612", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "fast_breathing", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 139, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "priority_flagging", "is_system_generated": 0, "is_virtual": 0, "label": "Fast Breathing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:42:59.587612", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-fast_breathing", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 04:46:24.304499", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "fetch_anthropometry_history", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 65, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "anthropometry_table", "is_system_generated": 0, "is_virtual": 0, "label": "Fetch Anthropometry History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-09-27 04:46:24.304499", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-fetch_anthropometry_history", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 14:51:39.060372", "default": null, "depends_on": "eval:(doc.has_no_food_allergy!=true)  ", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "food_allergy", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 100, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "has_no_food_allergy", "is_system_generated": 0, "is_virtual": 0, "label": "Food Allergy", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2023-02-07 09:30:21.434688", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-food_allergy", "no_copy": 0, "non_negative": 0, "options": "Patient Food Allergy", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-22 16:28:53.457805", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "gch_standard_treatment_guideline_mapping", "fieldtype": "Table", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 235, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pharmacy_scrollable_table", "is_system_generated": 0, "is_virtual": 0, "label": "GCH Standard Treatment Guideline Mapping", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:31:46.070434", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-gch_standard_treatment_guideline_mapping", "no_copy": 0, "non_negative": 0, "options": "GCH Standard Guideline Detail", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-22 16:27:29.961743", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "gch_standard_treatment_guidelines", "fieldtype": "Table", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 236, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "gch_standard_treatment_guideline_mapping", "is_system_generated": 0, "is_virtual": 0, "label": "GCH Standard Treatment Guidelines", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:32:32.264375", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-gch_standard_treatment_guidelines", "no_copy": 0, "non_negative": 0, "options": "GCH Standard Guideline Detail", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 10:06:26.043037", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "generate_bmi_for_age_chart", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 77, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bmi_for_age", "is_system_generated": 0, "is_virtual": 0, "label": "Generate BMI for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 10:06:26.043037", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-generate_bmi_for_age_chart", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 10:37:31.826872", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "generate_height_for_age_chart", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 79, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "height_for_age", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Height for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 10:37:31.826872", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-generate_height_for_age_chart", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 10:38:51.300540", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "generate_weight_for_age_chart", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 81, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "weight_for_age", "is_system_generated": 0, "is_virtual": 0, "label": "Generate Weight for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 10:38:51.300540", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-generate_weight_for_age_chart", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-22 16:29:05.602475", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "gertrudes_standard_treatment_guidelines_med_doses", "fieldtype": "Table", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 237, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "gch_standard_treatment_guidelines", "is_system_generated": 0, "is_virtual": 0, "label": "Gertrudes Standard Treatment Guidelines Med Doses", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-02-11 06:59:04.306610", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-gertrudes_standard_treatment_guidelines_med_doses", "no_copy": 0, "non_negative": 0, "options": "GCH Standard Guideline Detail", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:49.925766", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "get_kranium_history", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 201, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "physical_examinations", "is_system_generated": 0, "is_virtual": 0, "label": "Get Kranium History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-12-14 01:21:50.874730", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-get_kranium_history", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:02.913872", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Nurse\") || frappe.user.has_role(\"System Manager\") || frappe.user.has_role(\"GCH-Wellbaby Nurse\") || frappe.user.has_role(\"GCH-TriageNurse\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "growth_monitoring", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 175, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "done_by", "is_system_generated": 0, "is_virtual": 0, "label": "Development Monitoring", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-25 12:02:30.620130", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-growth_monitoring", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:55:17.294127", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "grunting", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 140, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "fast_breathing", "is_system_generated": 0, "is_virtual": 0, "label": "Grunting", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:55:17.294127", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-grunting", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-05 15:24:58.634662", "default": "0", "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "has_no_drug_allergy", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 95, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_ju2dv", "is_system_generated": 0, "is_virtual": 0, "label": "Has no drug allergy", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-01 06:27:11.825428", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-has_no_drug_allergy", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-05 15:24:35.389416", "default": "0", "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "has_no_food_allergy", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 99, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "allergy_cbreak", "is_system_generated": 0, "is_virtual": 0, "label": "Has no food allergy", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-11 13:17:41.018119", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-has_no_food_allergy", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:28:36.013765", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "head_circumference_in_centimeters", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 68, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "height_in_centimeters", "is_system_generated": 0, "is_virtual": 0, "label": "Head Circumference In Centimeters", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-20 12:28:36.013765", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-head_circumference_in_centimeters", "no_copy": 0, "non_negative": 0, "options": "", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "1", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-05 06:55:18.883858", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "height_for_age", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 78, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "generate_bmi_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Height for Age Percentile", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-05 08:45:25.667940", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-height_for_age", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 10:51:03.150465", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "height_for_age_chart", "fieldtype": "HTML", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 163, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vaccines", "is_system_generated": 0, "is_virtual": 0, "label": "Height for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 10:51:03.150465", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-height_for_age_chart", "no_copy": 0, "non_negative": 0, "options": "<canvas height=\"400\" id=\"heightAgeChart\" width=\"400\"></canvas>", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 10:53:30.096027", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "height_for_age_chart_gch", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 82, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "generate_weight_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Height for Age Chart GCH", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 10:53:30.096027", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-height_for_age_chart_gch", "no_copy": 0, "non_negative": 0, "options": "<canvas id=\"heightAgeChart\" width=\"400\" height=\"400\"></canvas>", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:32:14.525023", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "height_in_centimeters", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 67, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "weight_in_kilograms", "is_system_generated": 0, "is_virtual": 0, "label": "Height in Centimeters", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:!doc.__islocal && doc.workflow_state == 'Pending Triage'", "modified": "2021-12-02 15:12:35.967739", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-height_in_centimeters", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "0", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-29 13:14:13.892721", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "height_length_for_age_chart", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 84, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "weight_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Height Length for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-29 13:24:26.658808", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-height_length_for_age_chart", "no_copy": 0, "non_negative": 0, "options": "<div id=\"hlfa-chart\"></div>", "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:35:59.409171", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "hypoglycemia", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 136, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "hypothermia", "is_system_generated": 0, "is_virtual": 0, "label": "Hypoglycemia", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:35:59.409171", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-hypoglycemia", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:35:39.377091", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "hypothermia", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 135, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "artificial_airway", "is_system_generated": 0, "is_virtual": 0, "label": "Hypothermia", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:35:39.377091", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-hypothermia", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:36:39.834795", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "immediate_post_ictal_period", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 137, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "hypoglycemia", "is_system_generated": 0, "is_virtual": 0, "label": "Immediate Post Ictal Period", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:36:39.834795", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-immediate_post_ictal_period", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 07:31:11.806413", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Nurse\") || frappe.user.has_role(\"System Manager\") || frappe.user.has_role(\"GCH-Wellbaby Nurse\") || frappe.user.has_role(\"GCH-TriageNurse\") || frappe.user.has_role(\"GCH-Reception\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "immunization", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 159, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_wellbaby", "is_system_generated": 0, "is_virtual": 0, "label": "Vaccination", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 14:20:58.538796", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-immunization", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 9, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 07:56:39.224854", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "inpatient_service_referral_details", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 244, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "service_referral", "is_system_generated": 0, "is_virtual": 0, "label": "Inpatient Service Referral Details", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-09-27 07:56:39.224854", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-inpatient_service_referral_details", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-28 13:52:39.817977", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "default_insurance.outpatient_insurance_company", "fetch_if_empty": 0, "fieldname": "insurance_category_employer", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 189, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "insurance_category_name", "is_system_generated": 0, "is_virtual": 0, "label": "Outpatient Insurance Company", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:11:25.799947", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-insurance_category_employer", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-28 13:50:52.384364", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "default_insurance.display_name", "fetch_if_empty": 0, "fieldname": "insurance_category_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 188, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "suspended", "is_system_generated": 0, "is_virtual": 0, "label": "Insurance Category Name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:11:39.006284", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-insurance_category_name", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-04-04 09:41:18.027488", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "integrator", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 293, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "data_fetched_from_prev_enc", "is_system_generated": 0, "is_virtual": 0, "label": "integrator", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-04-03 22:02:08.044368", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-integrator", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:33:55.807101", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "intraosseous_line_in_place", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 132, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bulging_anterior_fontanelle", "is_system_generated": 0, "is_virtual": 0, "label": "Intraosseous Line in Place", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:41:17.055792", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-intraosseous_line_in_place", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-29 18:44:26.036794", "default": null, "depends_on": "eval:(doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_drug_allergy_patient", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 10, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_priority_patient", "is_system_generated": 0, "is_virtual": 0, "label": "Drug Allergy", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-01 09:58:23.249995", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_drug_allergy_patient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-29 18:29:27.693817", "default": null, "depends_on": "eval:(doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_emergency_patient", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 8, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "priority", "is_system_generated": 0, "is_virtual": 0, "label": "Emergency Patient", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-13 07:30:05.366450", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_emergency_patient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-29 18:33:42.901338", "default": null, "depends_on": "eval:(doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_fall_risk_patient", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 18, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_age", "is_system_generated": 0, "is_virtual": 0, "label": "Fall Risk Patient", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:57:37.181643", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_fall_risk_patient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-01 07:03:19.950274", "default": "1", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_first_time_reception", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 6, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_uhid", "is_system_generated": 0, "is_virtual": 0, "label": "Is first time reception", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:56:53.207726", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_first_time_reception", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-23 09:29:04.119466", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_first_time_wellbaby_save", "fieldtype": "Check", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 42, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_section_break_010h4", "is_system_generated": 0, "is_virtual": 0, "label": "is first time wellbaby save", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-23 09:32:37.183385", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_first_time_wellbaby_save", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-29 18:50:57.194616", "default": null, "depends_on": "eval:doc.patient", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_isolation_patient", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 19, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_fall_risk_patient", "is_system_generated": 0, "is_virtual": 0, "label": "Isolation", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-01 09:57:19.161269", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_isolation_patient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-22 16:29:24.485616", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_outpatient", "fieldtype": "Check", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 215, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_plan_of_action_notes", "is_system_generated": 0, "is_virtual": 0, "label": "is outpatient", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-22 13:37:05.300640", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_outpatient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-29 18:30:07.131370", "default": null, "depends_on": "eval:(doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_priority_patient", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 9, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_emergency_patient", "is_system_generated": 0, "is_virtual": 0, "label": "Priority Patient", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-01 09:57:00.100958", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_priority_patient", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-31 04:16:06.988827", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_referral", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 245, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "inpatient_service_referral_details", "is_system_generated": 0, "is_virtual": 0, "label": "Is referral", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-31 04:16:06.988827", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_referral", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-23 08:58:29.391082", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_with_doctor", "fieldtype": "Check", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 291, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_details", "is_system_generated": 0, "is_virtual": 0, "label": "Is With Doctor", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-10 10:44:00.084593", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-is_with_doctor", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-23 09:11:53.923982", "default": "  <div class=\"form-grid\">\n  <div class=\"grid-heading-row\">\n    <div class=\"grid-row\">\n      <div class=\"data-row row\">\n        <div class=\"col grid-static-col col-xs-3\" title=\"Kranium Encounter\">\n          <div class=\"field-area\" style=\"display: none;\"></div>\n          <div class=\"static-area ellipsis\">Kranium Encounter</div>\n        </div>\n        <div class=\"col grid-static-col col-xs-3\" title=\"Seen by Doctor\">\n          <div class=\"field-area\" style=\"display: none;\"></div>\n          <div class=\"static-area ellipsis\">Seen by Doctor</div>\n        </div>\n        <div class=\"col grid-static-col col-xs-3 grid-overflow-no-ellipsis\" title=\"Description\">\n          <div class=\"field-area\" style=\"display: none;\"></div>\n          <div class=\"static-area ellipsis\">Description</div>\n        </div>\n      </div>\n    </div>\n  </div>\n  <div class=\"grid-body\">\n    <div class=\"rows\">\n      <div class=\"grid-row\">\n        <div class=\"data-row row\">\n          <div class=\"col grid-static-col col-xs-3 bold\">\n            <div class=\"field-area\" style=\"display: none;\"></div>\n            <div class=\"static-area ellipsis\">\n              <a href=\"/app/medical-code/ICD-10-Diagnosis%20A02.9\">\n                ICD-10-Diagnosis A02.9</a>\n            </div>\n          </div>\n          <div class=\"col grid-static-col col-xs-3\">\n            <div class=\"field-area\" style=\"display: none;\"></div>\n            <div class=\"static-area ellipsis\">A02.9</div>\n          </div>\n          <div class=\"col grid-static-col col-xs-3 grid-overflow-no-ellipsis\">\n            <div class=\"field-area\" style=\"display: none;\"></div>\n            <div class=\"static-area ellipsis\">\n              Salmonella infection, unspecified\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"grid-empty text-center hidden\">\n      <img alt=\"Grid Empty State\" class=\"grid-empty-illustration\" src=\"/assets/frappe/images/ui-states/grid-empty-state.svg\">\n      No Data\n    </div>\n  </div>\n</div>", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "kranium_data_response", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 203, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "kranium_physican_exams_table", "is_system_generated": 0, "is_virtual": 0, "label": "kranium data response", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-16 15:02:47.256479", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-kranium_data_response", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-23 09:11:54.382592", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "kranium_physican_exams_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 202, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "get_kranium_history", "is_system_generated": 0, "is_virtual": 0, "label": "kranium physican exams table", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-16 15:17:05.935579", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-kranium_physican_exams_table", "no_copy": 0, "non_negative": 0, "options": "Kranium Physical Exams Notes", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:08.797144", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "left_ear_results", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 172, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "right_ear_results", "is_system_generated": 0, "is_virtual": 0, "label": "Left Ear Results", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 11:47:51.741282", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-left_ear_results", "no_copy": 0, "non_negative": 0, "options": "\nPass\nNoisy\nRefer", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:00:43.135136", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "major_trauma", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 147, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tiny_child", "is_system_generated": 0, "is_virtual": 0, "label": "Major <PERSON><PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:00:43.135136", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-major_trauma", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-25 13:45:19.685996", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "malnutrition_level", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 70, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "muac", "is_system_generated": 0, "is_virtual": 0, "label": "Malnutrition Level", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-25 13:45:19.685996", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-malnutrition_level", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:07:34.833594", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "malnutrition_severe_wasting", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 154, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "referral", "is_system_generated": 0, "is_virtual": 0, "label": "Malnutrition Severe Wasting", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:07:34.833594", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-malnutrition_severe_wasting", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-23 11:26:42.272439", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "medical_diagnosis", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 208, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "codification_table", "is_system_generated": 0, "is_virtual": 0, "label": "Medical Diagnosis", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-02-23 12:48:40.346302", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-medical_diagnosis", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 4, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-28 13:29:48.557268", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "membership_no", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 193, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "principal_member_name", "is_system_generated": 0, "is_virtual": 0, "label": "membership_no", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-12 06:27:54.672477", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-membership_no", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-20 06:29:50.104752", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "mode_of_payment", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 185, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "sales_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Mode of Payment", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:frappe.user.has_role(\"GCH-Reception\") && doc.workflow_state == \"Pending Billing\"", "modified": "2022-03-29 16:54:19.333688", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-mode_of_payment", "no_copy": 0, "non_negative": 0, "options": "\nCash\nInsurance", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-07-27 02:32:39.797752", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "muac", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 69, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "head_circumference_in_centimeters", "is_system_generated": 0, "is_virtual": 0, "label": "MUAC", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-07-27 02:32:39.797752", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-muac", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:45:52.258168", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "musculoskeletal", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 257, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "section_break_210", "is_system_generated": 0, "is_virtual": 0, "label": "Musculoskeletal", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-28 06:05:33.278015", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-musculoskeletal", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:34:25.634510", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "neck_stiffness", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 133, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "intraosseous_line_in_place", "is_system_generated": 0, "is_virtual": 0, "label": "Neck Stiffness", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-21 21:43:40.774393", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-neck_stiffness", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-06 14:32:29.993896", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "number_of_emergency_vitals", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 295, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "auth_token", "is_system_generated": 0, "is_virtual": 0, "label": "Number of Emergency Vitals", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-06 14:32:29.993896", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-number_of_emergency_vitals", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-06 14:37:12.388657", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "number_of_priority_vitals", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 296, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "number_of_emergency_vitals", "is_system_generated": 0, "is_virtual": 0, "label": "Number of Priority Vitals", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-06 14:37:12.388657", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-number_of_priority_vitals", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-23 08:48:07.911923", "default": "  ", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": " ", "fetch_if_empty": 0, "fieldname": "nurse_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 89, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nursing_chief_complaint", "is_system_generated": 0, "is_virtual": 0, "label": "Nurse Notes", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-03-06 09:05:37.430609", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nurse_notes", "no_copy": 0, "non_negative": 0, "options": " ", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-03-06 08:42:12.185383", "default": " ", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nurse_notes_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 87, "ignore_user_permissions": 1, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "triage_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Nurse Notes Table", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-03-06 08:42:12.185383", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nurse_notes_table", "no_copy": 0, "non_negative": 0, "options": "Nurse Notes Table", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-30 12:59:55.890821", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nursing_chief_complaint", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 88, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nurse_notes_table", "is_system_generated": 0, "is_virtual": 0, "label": "Nursing Chief <PERSON><PERSON><PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:!doc.__islocal && doc.workflow_state == 'Pending Triage' && !doc.clinic == 'Wellbaby - GCH'", "modified": "2021-12-02 15:14:44.968633", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nursing_chief_complaint", "no_copy": 0, "non_negative": 0, "options": "Nursing Chief <PERSON><PERSON><PERSON><PERSON>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-14 12:46:28.552124", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nursing_diagnosis", "fieldtype": "Section Break", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 210, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "diagnosis_table", "is_system_generated": 0, "is_virtual": 0, "label": "Nursing Diagnosis", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-24 14:07:37.389030", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nursing_diagnosis", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:46:06.130216", "default": null, "depends_on": "eval:doc.clinic != \"Walk-In - GCH\"", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nursing_notes", "fieldtype": "Small Text", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 267, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "triage_notes_cbreak", "is_system_generated": 0, "is_virtual": 0, "label": "Nursing Notes", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-06-14 13:06:50.802048", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nursing_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 18:01:55.689190", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nutrition_screening", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 102, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_column_break_fcqkq", "is_system_generated": 0, "is_virtual": 0, "label": "Nutrition Screening", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-24 13:54:18.566207", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nutrition_screening", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-07 14:37:01.570714", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nutrition_screening_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 103, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nutrition_screening", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-07 15:31:32.508963", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nutrition_screening_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 18:09:28.441647", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "nutritional_supplementation_or_specialized_feeding", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 104, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nutrition_screening_cbreak", "is_system_generated": 0, "is_virtual": 0, "label": "Is the patient on Nutritional supplementation or Specialized feeding techniques or Methods", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-07 15:31:45.966796", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-nutritional_supplementation_or_specialized_feeding", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:11:03.514463", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "obstructed_breathing", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 116, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "weak_or_absent_breathing", "is_system_generated": 0, "is_virtual": 0, "label": "Obstructed Breathing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:11:03.514463", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-obstructed_breathing", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:08:03.125608", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "oedeme_of_both_feet", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 155, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "malnutrition_severe_wasting", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON><PERSON><PERSON> of Both Feet", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:08:03.125608", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-oedeme_of_both_feet", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-30 12:03:14.472383", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "op_conclusions_at_the_end_of_treatment", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 274, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "outpatient_discharge_barrier_to_care_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Conclusions at the end of treatment", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.workflow_state == 'Pending Discharge'", "modified": "2021-11-30 12:11:20.398310", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-op_conclusions_at_the_end_of_treatment", "no_copy": 0, "non_negative": 0, "options": "Patient Discharge Conclusion", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 08:40:15.158494", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "op_discharge_has_barrier_to_care", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 271, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "out_patient_discharge", "is_system_generated": 0, "is_virtual": 0, "label": "No Barrier to Care", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-08-18 08:45:18.843095", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-op_discharge_has_barrier_to_care", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 08:54:38.639358", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "op_discharge_has_education", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 278, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_153", "is_system_generated": 0, "is_virtual": 0, "label": "No Patient Education", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:36:56.068999", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-op_discharge_has_education", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 21:08:16.078801", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "other_allergy", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 97, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "drug_allergy", "is_system_generated": 0, "is_virtual": 0, "label": "Other Allergy", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-26 22:06:53.918813", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-other_allergy", "no_copy": 0, "non_negative": 0, "options": "Patient Other Allergy", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-11-01 23:00:46.955159", "default": null, "depends_on": "eval:doc.rescheduling_reason == \"Other\"", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "other_rescheduling_reason", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 36, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "rescheduling_reason", "is_system_generated": 0, "is_virtual": 0, "label": "Other Rescheduling Reason", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.rescheduling_reason == \"Other\"", "modified": "2023-11-01 23:00:46.955159", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-other_rescheduling_reason", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:16:02.207459", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "out_patient_discharge", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 270, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "amended_from", "is_system_generated": 0, "is_virtual": 0, "label": "Outpatient Discharge", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 12:58:48.518453", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-out_patient_discharge", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:34:09.056374", "default": null, "depends_on": "eval:(doc.op_discharge_has_barrier_to_care!=true)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "outpatient_discharge_barrier_to_care_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 273, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "outpatient_discharge_barriers_to_care_", "is_system_generated": 0, "is_virtual": 0, "label": "Other Barriers to care Notes", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-01 06:47:48.739090", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-outpatient_discharge_barrier_to_care_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:29:40.005241", "default": null, "depends_on": "eval:(doc.op_discharge_has_barrier_to_care!=true)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "outpatient_discharge_barriers_to_care_", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 272, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "op_discharge_has_barrier_to_care", "is_system_generated": 0, "is_virtual": 0, "label": "Barriers to care or education", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.workflow_state == 'Pending Discharge'", "modified": "2021-11-25 11:25:49.924223", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-outpatient_discharge_barriers_to_care_", "no_copy": 0, "non_negative": 0, "options": "Pat<PERSON> to Care", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:35:17.140645", "default": null, "depends_on": "eval:(doc.op_discharge_has_education!=true)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "outpatient_discharge_patient_education", "fieldtype": "Table MultiSelect", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 279, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "op_discharge_has_education", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Education", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.workflow_state == 'Pending Discharge'", "modified": "2021-11-25 11:26:20.783079", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-outpatient_discharge_patient_education", "no_copy": 0, "non_negative": 0, "options": "Patient Discharge Education", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:36:32.005399", "default": null, "depends_on": "eval:(doc.op_discharge_has_education!=true)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "outpatient_discharge_patient_education_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 280, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "outpatient_discharge_patient_education", "is_system_generated": 0, "is_virtual": 0, "label": "Other Patient Education Notes", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-01 06:49:27.034964", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-outpatient_discharge_patient_education_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:43:36.331068", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "outpatient_discharge_patients_condition_at_discharge", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 281, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "outpatient_discharge_patient_education_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Patient condition at discharge", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.workflow_state == 'Pending Discharge'", "modified": "2021-12-02 15:56:10.239011", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-outpatient_discharge_patients_condition_at_discharge", "no_copy": 0, "non_negative": 0, "options": "\nImproved\nUnchanged\nDeteriorated\nDied", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:01:06.395912", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pain", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 148, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "major_trauma", "is_system_generated": 0, "is_virtual": 0, "label": "Pain", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:01:06.395912", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-pain", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-30 16:09:43.078309", "default": "", "depends_on": "eval:(doc.encounter_number && doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_assessment", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 108, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "assessment_tools", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Assessment", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-12-08 06:21:19.767422", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_assessment", "no_copy": 0, "non_negative": 0, "options": "Patient Assessment Template", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-12-02 16:13:16.876622", "default": null, "depends_on": "eval:(doc.encounter_number && doc.patient)", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_assessment_button", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 109, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_assessment", "is_system_generated": 0, "is_virtual": 0, "label": "Start Patient Assessment", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-12-08 06:22:16.213523", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_assessment_button", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 18:12:58.364764", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 11, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_drug_allergy_patient", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 18:15:03.660083", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": "", "columns": 0, "creation": "2021-07-20 10:36:55.743950", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_details", "fieldtype": "Section Break", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 290, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reclosed_by", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Details", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-06 16:40:49.931894", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_details", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-18 07:45:50.010531", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_discharge_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 268, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nursing_notes", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-18 07:52:45.656956", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_discharge_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-07 15:09:00.568729", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "patient.dob", "fetch_if_empty": 0, "fieldname": "patient_dob", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 16, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "dob", "is_system_generated": 0, "is_virtual": 0, "label": "Patient DOB", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-04-26 21:02:08.863710", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_dob", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-30 16:59:18.301326", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_encounter_assessment", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 110, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_assessment_button", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Encounter Assessment", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-12-08 05:57:50.354635", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_encounter_assessment", "no_copy": 0, "non_negative": 0, "options": "<div class=\"row\">\n  <div class=\"col-md-12\">\n    <div class=\"table-responsive-sm\">\n      <table class=\"table table-striped table-bordered \" style=\"font-size: 12px;\">\n        <thead>\n          <tr>\n            <th>Assessment No</th>\n            <th>Type</th>\n            <th>Score</th>\n            <th>Date</th>\n            <th>Time</th>\n            <th>Done by</th>\n            <th>Action</th>\n          </tr>\n        </thead>\n        <tbody class=\"encounter-assessment\"></tbody>\n      </table>\n    </div>\n  </div>\n</div>\n", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 10:49:31.258444", "default": "", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_history", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 198, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_history", "is_system_generated": 0, "is_virtual": 0, "label": "Patient History", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 12:24:15.999496", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_history", "no_copy": 0, "non_negative": 0, "options": "Patient Encounter History Details", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-05-18 11:46:15.934712", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_info", "fieldtype": "HTML", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 232, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "prescription_details_section", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Info", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-05-14 15:19:48.957861", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_info", "no_copy": 0, "non_negative": 0, "options": "<hr>\n<div class=\"form-row mb-4\">\n    <div class=\"col-md-6 \">\n   <strong> Name :</strong>  <span>{{doc.patient_name}}</span> <br>\n   <strong>UHID :</strong>    <span>{{doc.patient_uhid}}</span><br>\n   <strong> DOB :</strong>    <span>{{doc.patient_dob}}</span><br>\n   <strong> Age :</strong>    <span>{{doc.patient_age}}</span> <br>\n</div>\n    <div class=\"col-md-6\">\n       <strong>Weight (KG) :</strong><span>{{doc.weight_in_kilograms}}</span> <br>\n       <strong>Height (CM) :</strong><span>{{doc.height_in_centimeters}}</span><br>\n       <strong>BSA :</strong><span>{{doc.bsa}}</span><br>\n       <strong>Clinic :</strong><span>{{doc.clinic}}</span><br>\n    </div>\n</div>\n<div class=\"form-row mb-3\">\n    <div class=\"col-md-4 \">\n        <label for=\"patientDrugAllergy\"><strong>Drug Allergies ({{doc.drug_allergy.length}})</strong></label>\n        {% for allergy in doc.drug_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientDrugAllergy\">{{allergy.drug_allergy}}</span>\n        {% endfor %}\n    </div>\n    <div class=\"col-md-4 \">\n        <label for=\"patientFoodAllergy\"><strong>Food Allergies ({{doc.food_allergy.length}})</strong></label>\n        {% for allergy in doc.food_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientFoodAllergy\">{{allergy.food_allergy}}</span>\n        {% endfor %}\n    </div>\n    <div class=\"col-md-4 \">\n        <label for=\"patientFoodAllergy\"><strong>Other Allergies ({{doc.other_allergy.length}})</strong></label>\n        {% for allergy in doc.other_allergy %}\n        <span class=\"badge badge-pill badge-warning\" id=\"patientFoodAllergy\">{{allergy.other_allergy}}</span>\n        {% endfor %}\n    </div>\n</div>\n<hr>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-14 12:47:30.056263", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_nursing_diagnosis", "fieldtype": "Table", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 211, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nursing_diagnosis", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Nursing Diagnosis", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-14 12:47:30.056263", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_nursing_diagnosis", "no_copy": 0, "non_negative": 0, "options": "Nursing Diagnosis", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-23 12:42:01.772470", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_physical_examination", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 204, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "kranium_data_response", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Physical Examination", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-02-23 12:42:01.772470", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_physical_examination", "no_copy": 0, "non_negative": 0, "options": "Patient Encounter Physical Examination Details", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 10:54:04.424154", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_plan_of_action_notes", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 214, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "plan_of_action", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Plan of Action Notes", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-06 10:54:04.424154", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_plan_of_action_notes", "no_copy": 0, "non_negative": 0, "options": "Patient Encounter Plan of Action", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:45:44.042680", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "patient.uhid_code", "fetch_if_empty": 0, "fieldname": "patient_uhid", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 5, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 1, "insert_after": "patient_name", "is_system_generated": 0, "is_virtual": 0, "label": "Patient UHID Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-04-26 22:03:07.589942", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_uhid", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-29 16:03:07.062802", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payment_cb", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 190, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "insurance_category_employer", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:05:43.859435", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-payment_cb", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": "", "columns": 0, "creation": "2022-03-28 13:20:09.025229", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payment_details_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 183, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_payment", "is_system_generated": 0, "is_virtual": 0, "label": "Payment Details Section", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 17:32:43.960131", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-payment_details_section", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 8, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-05-06 08:12:05.338487", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pharmacy_dispensement_form", "fieldtype": "<PERSON><PERSON>", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 238, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "gertrudes_standard_treatment_guidelines_med_doses", "is_system_generated": 0, "is_virtual": 0, "label": "Pharmacy Dispensement Form", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-05-26 09:54:49.669242", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-pharmacy_dispensement_form", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-05-06 08:12:06.083928", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pharmacy_scrollable_table", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 234, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "diag_field", "is_system_generated": 0, "is_virtual": 0, "label": "Pharmacy Scrollable Table", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-05-05 13:27:40.369310", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-pharmacy_scrollable_table", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 6, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-14 14:57:20.721933", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "phone_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 194, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "membership_no", "is_system_generated": 0, "is_virtual": 0, "label": "Phone Number", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 17:31:55.497711", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-phone_number", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-23 12:40:54.306967", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "physical_examinations", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 200, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_physical_examination", "is_system_generated": 0, "is_virtual": 0, "label": "Physical Examinations", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-02-23 12:40:54.306967", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-physical_examinations", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 3, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-23 06:22:25.779221", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "plan_of_action", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 213, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_plan_of_action", "is_system_generated": 0, "is_virtual": 0, "label": "Care Objectives or Plan of Action", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 12:54:39.173362", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-plan_of_action", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-05-30 12:48:51.520281", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "plan_of_action_notes", "fieldtype": "Small Text", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 299, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_section_break_wewje", "is_system_generated": 0, "is_virtual": 0, "label": "Plan of Action Notes", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-23 06:26:03.199268", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-plan_of_action_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:01:58.926106", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "poisoning", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 149, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pain", "is_system_generated": 0, "is_virtual": 0, "label": "Poisoning Caregiver Report", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:02:03.240407", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-poisoning", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-24 16:30:53.515515", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "prescription_details_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 231, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "drug_prescription", "is_system_generated": 0, "is_virtual": 0, "label": "Prescription Details Section", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:39:23.079822", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-prescription_details_section", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 1, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-11 06:59:05.279869", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Doctor\") || frappe.user.has_role(\"System Manager\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "prescription_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 240, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "allergies_doctor_view", "is_system_generated": 0, "is_virtual": 0, "label": "Prescription Table", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-07-27 03:32:59.059742", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-prescription_table", "no_copy": 0, "non_negative": 0, "options": "Doctor Prescription Table", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-23 09:11:54.739846", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "prescription_total", "fieldtype": "Int", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 229, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "sb_drug_prescription", "is_system_generated": 0, "is_virtual": 0, "label": "Prescription Total", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-01-13 16:04:25.454821", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-prescription_total", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-13 05:18:13.746515", "default": null, "depends_on": " ", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "primary_doctor", "fieldtype": "Link", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 22, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_number", "is_system_generated": 0, "is_virtual": 0, "label": "Primary Doctor", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-13 05:19:02.644183", "modified_by": "Administrator", "module": null, "name": "Patient <PERSON>unter-primary_doctor", "no_copy": 0, "non_negative": 0, "options": "Healthcare Practitioner", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-13 05:12:12.051082", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "primary_doctor_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 23, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "primary_doctor", "is_system_generated": 0, "is_virtual": 0, "label": "Primary Doctor Name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-13 05:12:12.051082", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-primary_doctor_name", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-28 13:28:44.828015", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "principal_member", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 191, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_cb", "is_system_generated": 0, "is_virtual": 0, "label": "principal_member", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-12 09:15:23.752768", "modified_by": "Administrator", "module": null, "name": "Pat<PERSON>unter-principal_member", "no_copy": 0, "non_negative": 0, "options": "Parent", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-03-28 13:45:53.012952", "default": null, "depends_on": "eval:(doc.mode_of_payment==\"Insurance\")", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "principal_member.full_name", "fetch_if_empty": 0, "fieldname": "principal_member_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 192, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "principal_member", "is_system_generated": 0, "is_virtual": 0, "label": "principal_member_name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2023-10-12 09:13:42.952723", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-principal_member_name", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-03-12 11:24:37.586933", "default": " ", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "print_format_selector", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 297, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "number_of_priority_vitals", "is_system_generated": 0, "is_virtual": 0, "label": "Print Format Selector", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-12 11:24:37.586933", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-print_format_selector", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-17 06:54:13.786797", "default": "2", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "priority", "fieldtype": "Data", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 7, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_first_time_reception", "is_system_generated": 0, "is_virtual": 0, "label": "Priority", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-02-17 06:54:13.786797", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-priority", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:58:52.617817", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "priority_cbreak1", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 145, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "drooling", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:58:58.046320", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-priority_cbreak1", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:04:11.458282", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "priority_cbreak2", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 152, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "restless_irritable_floppy", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:04:15.524218", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-priority_cbreak2", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:42:22.230782", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "priority_flagging", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 138, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "immediate_post_ictal_period", "is_system_generated": 0, "is_virtual": 0, "label": "Priority Flagging", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:42:25.921570", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-priority_flagging", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:19:58.794498", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pulse_rate_lt_60_per_min", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 123, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "capillary_return_time_gt_3_sec", "is_system_generated": 0, "is_virtual": 0, "label": "Pulse Rate Less than 60 per min", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-30 10:38:00.072375", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-pulse_rate_lt_60_per_min", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": "", "columns": 0, "creation": "2021-08-04 21:07:02.799648", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "queue_group", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 195, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "phone_number", "is_system_generated": 0, "is_virtual": 0, "label": "Queue Group", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-29 17:35:55.467687", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-queue_group", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-23 06:35:43.682673", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "radiology", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 223, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "lab_test_prescription", "is_system_generated": 0, "is_virtual": 0, "label": "Radiology", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-24 16:45:39.719210", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-radiology", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 4, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-23 06:38:04.171092", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "radiology_details", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 224, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "radiology", "is_system_generated": 0, "is_virtual": 0, "label": "Radiology Details", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-21 17:32:40.499116", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-radiology_details", "no_copy": 0, "non_negative": 0, "options": "Radiology Prescriptions", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 08:02:26.136669", "default": null, "depends_on": "eval:doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reason_for_referral", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 249, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "treatment_given", "is_system_generated": 0, "is_virtual": 0, "label": "Reason for Referral", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.is_referral == 1", "modified": "2022-09-27 08:02:26.136669", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-reason_for_referral", "no_copy": 0, "non_negative": 0, "options": "\nFinancial\nProximity\nPreference\nNo beds\nUnspecified\nOther", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:22:50.480184", "default": null, "depends_on": "eval:doc.revisit_reason_dur >= 0 && doc.revisit_reason_dur < 24 && doc.clinic != \"Wellbaby Rescheduled Vaccination - GCH\"", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reason_for_visit", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 51, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "encounter_closed_by", "is_system_generated": 0, "is_virtual": 0, "label": "Revisit Reason", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.revisit_reason_dur >= 0 && doc.revisit_reason_dur < 24 && doc.clinic != \"Wellbaby Rescheduled Vaccination - GCH\"", "modified": "2023-11-01 23:08:16.190900", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-reason_for_visit", "no_copy": 0, "non_negative": 0, "options": "\nPatient not improving\n<PERSON><PERSON> deteriorated\nNew presentation\nFollow-up", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-29 17:34:16.920649", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "received_vaccines", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 165, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vaccination_record", "is_system_generated": 0, "is_virtual": 0, "label": "Received Vaccines", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-29 17:39:03.374411", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-received_vaccines", "no_copy": 0, "non_negative": 0, "options": "<div class=\"row\">\n    <div class=\"col-md-12\">\n        <table class=\"table table-striped table-sm\">\n            <thead>\n                <tr>\n                    <th>Vaccine</th>\n                    <th>Date Administered</th>\n                </tr>\n            </thead>\n            <tbody class=\"karani\"></tbody>\n        </table>\n    </div>\n</div>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:48.183240", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reclosed_by", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 289, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reclosing_date_and_time", "is_system_generated": 0, "is_virtual": 0, "label": "Reclosed by", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:49:41.266072", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-reclosed_by", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:47.910136", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reclosing_date_and_time", "fieldtype": "Datetime", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 288, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reopened_by", "is_system_generated": 0, "is_virtual": 0, "label": "Reclosing Date and Time", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:49:39.931932", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-reclosing_date_and_time", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:05:07.528674", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "referral", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 153, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "priority_cbreak2", "is_system_generated": 0, "is_virtual": 0, "label": "Urgent Referral Letter", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 16:56:01.842855", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-referral", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 08:02:24.566413", "default": null, "depends_on": "eval:doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "referred_to_doctor", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 247, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "referred_to_hospital", "is_system_generated": 0, "is_virtual": 0, "label": "Referred To Doctor", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.is_referral == 1", "modified": "2022-09-27 08:02:24.566413", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-referred_to_doctor", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 08:02:23.717776", "default": null, "depends_on": "eval:doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "referred_to_hospital", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 246, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_referral", "is_system_generated": 0, "is_virtual": 0, "label": "Referred To Hospital", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.is_referral == 1", "modified": "2022-09-27 08:02:23.717776", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-referred_to_hospital", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:07.283303", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "relative_with_ear_surgery", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 167, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wellbaby_audiology", "is_system_generated": 0, "is_virtual": 0, "label": "Relative with Ear Surgery", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-23 17:57:58.928027", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-relative_with_ear_surgery", "no_copy": 0, "non_negative": 0, "options": "\nNo\nYes", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:07.792955", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "relative_with_hearing_devices", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 169, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "relative_with_hearing_loss", "is_system_generated": 0, "is_virtual": 0, "label": "Relative with Hearing Devices", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-23 17:56:07.370584", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-relative_with_hearing_devices", "no_copy": 0, "non_negative": 0, "options": "\nNo\nYes", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:07.551211", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "relative_with_hearing_loss", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 168, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "relative_with_ear_surgery", "is_system_generated": 0, "is_virtual": 0, "label": "Relative with Hearing Loss", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-22 10:54:38.788793", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-relative_with_hearing_loss", "no_copy": 0, "non_negative": 0, "options": "\nNo\nYes", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:47.278407", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reopen_reason", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 286, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "date_and_time", "is_system_generated": 0, "is_virtual": 0, "label": "Reopen Reason", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:49:37.394031", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-reopen_reason", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-01-02 20:41:47.553927", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "reopened_by", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 287, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "reopen_reason", "is_system_generated": 0, "is_virtual": 0, "label": "Reopened by", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-11-08 15:49:38.671903", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-reopened_by", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-11-01 22:57:30.370199", "default": null, "depends_on": "eval:doc.clinic == \"Wellbaby Rescheduled Vaccination - GCH\"", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "rescheduling_reason", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 35, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "clinic", "is_system_generated": 0, "is_virtual": 0, "label": "Rescheduling Reason", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.clinic == \"Wellbaby Rescheduled Vaccination - GCH\"", "modified": "2023-11-01 22:57:30.370199", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-rescheduling_reason", "no_copy": 0, "non_negative": 0, "options": "\nChild unwell\nChild not of age for vaccine\nCoadministration contradiction\nClient request\nOther", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:03:25.041648", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "restless_irritable_floppy", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 151, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "severe_palmar_pallor", "is_system_generated": 0, "is_virtual": 0, "label": "Restless Irritable Floppy", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:03:25.041648", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-restless_irritable_floppy", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-06 12:25:23.445264", "default": "-1", "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "revisit_reason_dur", "fieldtype": "Int", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 48, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "section_break_153", "is_system_generated": 0, "is_virtual": 0, "label": "Hours Since Last Visit", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2023-01-06 16:34:10.843694", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-revisit_reason_dur", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:08.296657", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "right_ear_results", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 171, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wellbaby_audiology_column_break", "is_system_generated": 0, "is_virtual": 0, "label": "Right Ear Results", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 11:48:01.048460", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-right_ear_results", "no_copy": 0, "non_negative": 0, "options": "\nPass\nNoisy\nRefer", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 18:13:17.491362", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "routine_nutritional_counselling_not_done_at_6_months", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 106, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "unintentional_weight_loss", "is_system_generated": 0, "is_virtual": 0, "label": "Was Routine Nutritional Counselling at age of 6 months done", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-14 13:25:22.953439", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-routine_nutritional_counselling_not_done_at_6_months", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-31 16:51:03.116879", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "sales_invoice", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 184, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_details_section", "is_system_generated": 0, "is_virtual": 0, "label": "Sales Invoice", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-03-29 16:54:06.677664", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sales_invoice", "no_copy": 0, "non_negative": 0, "options": "Sales Invoice", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-13 18:06:46.119154", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_128", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 261, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "symptoms_in_print", "is_system_generated": 0, "is_virtual": 0, "label": null, "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-13 18:06:46.119154", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-section_break_128", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:45:40.988570", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_153", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 47, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "appointment_type", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-04 16:14:36.691994", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-section_break_153", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:45:41.868881", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_162", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 2, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_details_tab", "is_system_generated": 0, "is_virtual": 0, "label": "Patient Details", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-03 13:03:05.002037", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-section_break_162", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 1, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-04-28 11:45:51.660722", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_210", "fieldtype": "Section Break", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 256, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_tab_10", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-06 12:30:48.564194", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-section_break_210", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-14 13:13:21.888930", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "service_referral", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 243, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "service_referrals", "is_system_generated": 0, "is_virtual": 0, "label": "Service Referral", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-14 13:13:21.888930", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-service_referral", "no_copy": 0, "non_negative": 0, "options": "Patient Service Referral", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-11 10:58:49.706373", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "service_referrals", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 242, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_referrals", "is_system_generated": 0, "is_virtual": 0, "label": "Service Referrals", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-11 11:05:47.467042", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-service_referrals", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:08:36.285543", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "severe_burns", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 156, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "oedeme_of_both_feet", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:08:36.285543", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-severe_burns", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:02:53.346244", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "severe_palmar_pallor", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 150, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "poisoning", "is_system_generated": 0, "is_virtual": 0, "label": "Severe <PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:02:53.346244", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-severe_palmar_pallor", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:14:36.811742", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "severe_respiratory_distress", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 118, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "central_cyanosis_or_spo2", "is_system_generated": 0, "is_virtual": 0, "label": "Severe Respiratory Distress", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:31:51.765200", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-severe_respiratory_distress", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:57:36.478650", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "stridor", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 143, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wheeze", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON><PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:57:36.478650", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-stridor", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-12-23 07:34:39.179232", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "default_insurance.suspend", "fetch_if_empty": 0, "fieldname": "suspended", "fieldtype": "Check", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 187, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "default_insurance", "is_system_generated": 0, "is_virtual": 0, "label": "Suspended", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-12-23 07:34:39.179232", "modified_by": "Administrator", "module": null, "name": "Pat<PERSON> Encounter-suspended", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:59:33.443581", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "tiny_child", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 146, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "priority_cbreak1", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON> Child Less than 2 months old", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:59:55.494696", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-tiny_child", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-23 05:42:40.556656", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "traige_drugs_administered", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 90, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nurse_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Triage Drugs Administered", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-09-10 13:39:55.612528", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-traige_drugs_administered", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-27 08:02:25.375839", "default": null, "depends_on": "eval:doc.is_referral == 1", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "treatment_given", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 248, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "referred_to_doctor", "is_system_generated": 0, "is_virtual": 0, "label": "Treatment Given (IF ANY)", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-09-27 08:02:25.375839", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-treatment_given", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-23 05:47:45.370533", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "triage_medication", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 91, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "traige_drugs_administered", "is_system_generated": 0, "is_virtual": 0, "label": "Triage Medication", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-21 17:42:45.495951", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-triage_medication", "no_copy": 0, "non_negative": 0, "options": "Triage Drug Administered", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 16:49:08.021356", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "triage_notes", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 86, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_nursing_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Nursing Notes", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-24 13:57:57.904971", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-triage_notes", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 8, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 21:42:14.820130", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "triage_notes_cbreak", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 266, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "sb_refs", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-30 13:10:08.063291", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-triage_notes_cbreak", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 12:09:13.623626", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "unable_to_drink_or_vomits_anything", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 157, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "severe_burns", "is_system_generated": 0, "is_virtual": 0, "label": "Unable to drink or Vomits anything", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 12:09:13.623626", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-unable_to_drink_or_vomits_anything", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 18:07:41.128053", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "unintentional_weight_loss", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 105, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nutritional_supplementation_or_specialized_feeding", "is_system_generated": 0, "is_virtual": 0, "label": "Has there been unintentional Weight loss in the past 1 month", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-01-07 15:31:57.510588", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-unintentional_weight_loss", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": "", "columns": 0, "creation": "2021-11-01 05:48:01.849883", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Nurse\") || frappe.user.has_role(\"System Manager\") || frappe.user.has_role(\"GCH-Wellbaby Nurse\") || frappe.user.has_role(\"GCH-TriageNurse\")", "description": "", "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vaccination_record", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 164, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "height_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Vaccination Record", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-24 14:22:48.627724", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-vaccination_record", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:02.443817", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vaccines", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 162, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "visit_schedule", "is_system_generated": 0, "is_virtual": 0, "label": "Vaccines", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-25 13:17:02.063446", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-vaccines", "no_copy": 0, "non_negative": 0, "options": "Wellbaby Vaccine Details", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-08-14 11:57:17.675886", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "view_results", "fieldtype": "<PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 300, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "plan_of_action_notes", "is_system_generated": 0, "is_virtual": 0, "label": "View Results", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-08-14 11:57:17.675886", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-view_results", "no_copy": 0, "non_negative": 0, "options": null, "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:47:59.396206", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "visit_schedule", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 161, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "child_fit_for_vaccination", "is_system_generated": 0, "is_virtual": 0, "label": "Visit Schedule", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:doc.child_fit_for_vaccination == \"Yes\"", "modified": "2021-11-25 13:16:46.630955", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-visit_schedule", "no_copy": 0, "non_negative": 0, "options": "Vaccine Administration", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 15:21:49.195147", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vital_signs", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 53, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_triage", "is_system_generated": 0, "is_virtual": 0, "label": "Vital Signs", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-20 16:20:54.071751", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-vital_signs", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-12-02 05:41:44.438278", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vital_signs_chart", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 62, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vital_signs_table", "is_system_generated": 0, "is_virtual": 0, "label": "Vital Signs Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-12-02 06:05:36.590265", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-vital_signs_chart", "no_copy": 0, "non_negative": 0, "options": "<div id=\"vital-signs-chart\"></div>", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-06 13:11:15.873562", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vital_signs_table", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 61, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "critical_vital_bpdiastolic", "is_system_generated": 0, "is_virtual": 0, "label": "Vital Signs", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:!doc.__islocal && doc.workflow_state == 'Pending Triage'", "modified": "2021-12-08 14:28:36.596846", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-vital_signs_table", "no_copy": 0, "non_negative": 0, "options": "Patient Encounter Vital Signs", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:10:17.237656", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "weak_or_absent_breathing", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 115, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "airway_and_breathing", "is_system_generated": 0, "is_virtual": 0, "label": "Weak or Absent Breathing", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:10:17.237656", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-weak_or_absent_breathing", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:16:58.751855", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "weak_or_fast_pulse_gt_160", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 121, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "cold_hands_with", "is_system_generated": 0, "is_virtual": 0, "label": "Weak or Fast pulse Greater than 160 per min", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:18:09.186325", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-weak_or_fast_pulse_gt_160", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-05 05:42:05.692899", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "weight_for_age", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 80, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "generate_height_for_age_chart", "is_system_generated": 0, "is_virtual": 0, "label": "Weight for Age Percentile", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-11-11 12:44:59.884047", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-weight_for_age", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-07 11:45:10.881153", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "weight_for_age_chart", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 83, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "height_for_age_chart_gch", "is_system_generated": 0, "is_virtual": 0, "label": "Weight for Age Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-10-07 11:45:10.881153", "modified_by": "<EMAIL>", "module": null, "name": "Patient Encounter-weight_for_age_chart", "no_copy": 0, "non_negative": 0, "options": "<canvas height=\"400\" id=\"weightAgeChart\" width=\"400\"></canvas>", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-20 12:26:55.773388", "default": null, "depends_on": "", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "weight_in_kilograms", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 66, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "fetch_anthropometry_history", "is_system_generated": 0, "is_virtual": 0, "label": "Weight In Kilograms", "length": 0, "link_filters": null, "mandatory_depends_on": "eval:!doc.__islocal && doc.workflow_state == 'Pending Triage'", "modified": "2021-12-02 15:12:47.204820", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-weight_in_kilograms", "no_copy": 0, "non_negative": 0, "options": "", "owner": "<EMAIL>", "permlevel": 0, "placeholder": null, "precision": "1", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 13:02:34.197216", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wellbaby_advice_on_social_and_behavioral_development", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 282, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "outpatient_discharge_patients_condition_at_discharge", "is_system_generated": 0, "is_virtual": 0, "label": "Advice on Social and Behavioral Development", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-29 17:23:30.170539", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wellbaby_advice_on_social_and_behavioral_development", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:07.037230", "default": null, "depends_on": "eval:frappe.user.has_role(\"GCH-Nurse\") || frappe.user.has_role(\"System Manager\") || frappe.user.has_role(\"GCH-Wellbaby Nurse\") || frappe.user.has_role(\"GCH-TriageNurse\")", "description": "", "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wellbaby_audiology", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 166, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "received_vaccines", "is_system_generated": 0, "is_virtual": 0, "label": "Wellbaby Audiology", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-03-23 17:47:55.821950", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wellbaby_audiology", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 2, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 05:48:08.042342", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wellbaby_audiology_column_break", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 170, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "relative_with_hearing_devices", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-10-22 20:24:29.006019", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wellbaby_audiology_column_break", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-11-01 12:35:23.954883", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wellbaby_child_safety_advice", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 275, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "op_conclusions_at_the_end_of_treatment", "is_system_generated": 0, "is_virtual": 0, "label": "Child Safety Advice", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2021-11-25 14:00:25.301518", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wellbaby_child_safety_advice", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-02-01 08:40:34.883345", "default": null, "depends_on": "eval:doc.clinic == 'Wellbaby - GCH'", "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wellbaby_nutrition_counselling", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 276, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wellbaby_child_safety_advice", "is_system_generated": 0, "is_virtual": 0, "label": "Nutrition Counselling", "length": 0, "link_filters": null, "mandatory_depends_on": "", "modified": "2022-02-23 13:00:40.550727", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wellbaby_nutrition_counselling", "no_copy": 0, "non_negative": 0, "options": "\nYes\nNo", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-07-21 11:56:27.294713", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wheeze", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 142, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "chest_wall_indrawing", "is_system_generated": 0, "is_virtual": 0, "label": "Wheeze", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-07-21 11:56:27.294713", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-wheeze", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2021-08-04 21:07:03.512115", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "workflow_state", "fieldtype": "Link", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 283, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wellbaby_advice_on_social_and_behavioral_development", "is_system_generated": 0, "is_virtual": 0, "label": "Workflow State", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2021-08-04 21:07:03.512115", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-workflow_state", "no_copy": 1, "non_negative": 0, "options": "Workflow State", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-01-28 05:54:06.412523", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Encounter", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "z_score_chart", "fieldtype": "HTML", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 72, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bsa", "is_system_generated": 0, "is_virtual": 0, "label": "Z Score Chart", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2022-01-28 15:55:44.148616", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-z_score_chart", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Patient Encounter", "links": [{"creation": "2025-03-19 16:34:51.866817", "custom": 0, "docstatus": 0, "group": "Laboratories", "hidden": 0, "idx": 0, "is_child_table": 0, "link_doctype": "Lab Test", "link_fieldname": "patient_encounter", "modified": "2023-03-06 11:14:55.729162", "modified_by": "Administrator", "name": "3b3fe1f240", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}, {"creation": "2016-04-21 10:53:44.637684", "custom": 0, "docstatus": 0, "group": "Laboratories", "hidden": 0, "idx": 1, "is_child_table": 0, "link_doctype": "Lab Test", "link_fieldname": "patient_encounter", "modified": "2025-03-14 12:46:53.154375", "modified_by": "Administrator", "name": "3sdi8g89hn", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}, {"creation": "2025-03-19 16:34:51.812208", "custom": 1, "docstatus": 0, "group": "Sales", "hidden": 0, "idx": 2, "is_child_table": 0, "link_doctype": "Prescription Returns", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.825872", "modified_by": "Administrator", "name": "6716137ed0", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2016-04-21 10:53:44.637684", "custom": 0, "docstatus": 0, "group": null, "hidden": 0, "idx": 3, "is_child_table": 0, "link_doctype": "Treatment Counselling", "link_fieldname": "admission_encounter", "modified": "2025-03-14 12:46:53.154375", "modified_by": "Administrator", "name": "7ak0fapmit", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}, {"creation": "2016-04-21 10:53:44.637684", "custom": 0, "docstatus": 0, "group": null, "hidden": 0, "idx": 2, "is_child_table": 0, "link_doctype": "Clinical Note", "link_fieldname": "reference_name", "modified": "2025-03-14 12:46:53.154375", "modified_by": "Administrator", "name": "8dmn7jmlmo", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}, {"creation": "2025-03-19 16:34:51.804592", "custom": 1, "docstatus": 0, "group": "Records", "hidden": 0, "idx": 4, "is_child_table": 0, "link_doctype": "Clinical Record", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.831992", "modified_by": "Administrator", "name": "a0516ea667", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2025-03-19 16:34:51.820555", "custom": 1, "docstatus": 0, "group": "Patient Consent", "hidden": 0, "idx": 3, "is_child_table": 0, "link_doctype": "Consent Form", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.829035", "modified_by": "Administrator", "name": "a0614ea993", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2025-03-19 16:34:51.795685", "custom": 1, "docstatus": 0, "group": "Specialist Clinic", "hidden": 0, "idx": 5, "is_child_table": 0, "link_doctype": "Dental Clinic Procedure", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.834874", "modified_by": "Administrator", "name": "a0883ea877", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2025-03-19 16:34:51.829133", "custom": 1, "docstatus": 0, "group": "Patient Consent", "hidden": 0, "idx": 1, "is_child_table": 0, "link_doctype": "SURGICAL SAFETY CHECKLIST", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.811712", "modified_by": "Administrator", "name": "d0289nm734h", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2025-03-20 15:30:53.898910", "custom": 1, "docstatus": 0, "group": "Clinical Procedure", "hidden": 0, "idx": 10, "is_child_table": 0, "link_doctype": "Clinical Procedure", "link_fieldname": "patient_encounter", "modified": "2025-03-25 11:21:48.844345", "modified_by": "Administrator", "name": "i53m2n9j9r", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}, {"creation": "2025-03-20 15:26:21.491444", "custom": 1, "docstatus": 0, "group": "Patient Invoice", "hidden": 0, "idx": 9, "is_child_table": 0, "link_doctype": "Sales Invoice", "link_fieldname": "encounter", "modified": "2025-03-25 11:21:48.841403", "modified_by": "Administrator", "name": "ucel4q3l1u", "owner": "Administrator", "parent": "Patient Encounter", "parent_doctype": null, "parentfield": "links", "parenttype": "Customize Form", "table_fieldname": null}], "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-02-22 09:04:55.591795", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment_type", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.642234", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-appointment_type-depends_on", "owner": "Administrator", "property": "depends_on", "property_type": "Data", "row_name": null, "value": " "}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:55.787343", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment_type", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.652196", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-appointment_type-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-01-06 16:38:15.386771", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.662726", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-appointment-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-01-06 16:38:16.149926", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.672883", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-appointment-read_only", "owner": "Administrator", "property": "read_only", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-25 10:53:44.452171", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "codification_table", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.682952", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-codification_table-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-23 06:14:25.939414", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "codification", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.693026", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-codification-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Diagnosis"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-08-01 13:07:39.579330", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "column_break_6", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.704481", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-column_break_6-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-08-01 17:02:41.687807", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "column_break_6", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.714796", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-column_break_6-in_preview", "owner": "Administrator", "property": "in_preview", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-08-01 17:02:41.925746", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "column_break_6", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.724916", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-column_break_6-read_only", "owner": "Administrator", "property": "read_only", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.143834", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.734884", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-company-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:57.377996", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "diagnosis_in_print", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.744900", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diagnosis_in_print-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-19 15:12:02.444361", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "diagnosis_tab", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 15:12:02.444361", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diagnosis_tab-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:57.255654", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "diagnosis", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.754867", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-diagnosis-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-03-10 05:56:55.739995", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "drug_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.764935", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-drug_prescription-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.743067", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "drug_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.774844", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-drug_prescription-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Medication"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-03-10 05:56:56.148296", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "drug_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.785144", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-drug_prescription-read_only", "owner": "Administrator", "property": "read_only", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-26 22:48:34.001982", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "encounter_comment", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.795139", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_comment-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-07 16:54:04.839258", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "encounter_details_tab", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.172732", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_details_tab-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-07 16:49:30.769236", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "encounter_details_tab", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.182999", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_details_tab-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Clinic"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-28 05:57:48.049916", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "encounter_time", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.804913", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_time-default", "owner": "Administrator", "property": "default", "property_type": "Text", "row_name": null, "value": "Now"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.261186", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "encounter_time", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.815117", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-encounter_time-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Encounter Start Time"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.393492", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "invoiced", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.825020", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-invoiced-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-11 12:31:09.651845", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.223030", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-autoname", "owner": "Administrator", "property": "autoname", "property_type": "Data", "row_name": null, "value": "hash"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-09-21 12:24:15.540300", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.835144", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-default_print_format", "owner": "<EMAIL>", "property": "default_print_format", "property_type": "Data", "row_name": null, "value": "Doctor Prescription"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-25 11:21:35.386515", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-25 11:21:35.386515", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-field_order", "owner": "Administrator", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"patient_details_tab\", \"section_break_162\", \"patient\", \"patient_name\", \"patient_uhid\", \"is_first_time_reception\", \"priority\", \"is_emergency_patient\", \"is_priority_patient\", \"is_drug_allergy_patient\", \"patient_cbreak\", \"patient_sex\", \"date_of_birth\", \"change_date_of_birth\", \"dob\", \"patient_dob\", \"patient_age\", \"is_fall_risk_patient\", \"is_isolation_patient\", \"encounter_information\", \"encounter_number\", \"primary_doctor\", \"primary_doctor_name\", \"inpatient_record\", \"inpatient_status\", \"column_break_6\", \"company\", \"branch\", \"status\", \"encounter_date\", \"encounter_time\", \"practitioner\", \"practitioner_name\", \"clinic\", \"rescheduling_reason\", \"other_rescheduling_reason\", \"medical_department\", \"google_meet_link\", \"invoiced\", \"submit_orders_on_save\", \"custom_section_break_010h4\", \"is_first_time_wellbaby_save\", \"naming_series\", \"title\", \"appointment\", \"appointment_type\", \"section_break_153\", \"revisit_reason_dur\", \"closing_date_and_time\", \"encounter_closed_by\", \"reason_for_visit\", \"custom_triage\", \"vital_signs\", \"critical_vital_signs\", \"critical_vital_temperature\", \"critical_vital_heart_rate\", \"critical_vital_respiratory_rate\", \"critical_vital_oxygen_saturation\", \"critical_vital_bpsystolic\", \"critical_vital_bpdiastolic\", \"vital_signs_table\", \"vital_signs_chart\", \"anthropometry\", \"anthropometry_table\", \"fetch_anthropometry_history\", \"weight_in_kilograms\", \"height_in_centimeters\", \"head_circumference_in_centimeters\", \"muac\", \"malnutrition_level\", \"bsa\", \"z_score_chart\", \"bmi_for_age_chart\", \"anthropometry_break\", \"bmi\", \"bmi_for_age\", \"generate_bmi_for_age_chart\", \"height_for_age\", \"generate_height_for_age_chart\", \"weight_for_age\", \"generate_weight_for_age_chart\", \"height_for_age_chart_gch\", \"weight_for_age_chart\", \"height_length_for_age_chart\", \"custom_nursing_notes\", \"triage_notes\", \"nurse_notes_table\", \"nursing_chief_complaint\", \"nurse_notes\", \"traige_drugs_administered\", \"triage_medication\", \"custom_assessment\", \"allergies\", \"custom_column_break_ju2dv\", \"has_no_drug_allergy\", \"drug_allergy\", \"other_allergy\", \"allergy_cbreak\", \"has_no_food_allergy\", \"food_allergy\", \"custom_column_break_fcqkq\", \"nutrition_screening\", \"nutrition_screening_cbreak\", \"nutritional_supplementation_or_specialized_feeding\", \"unintentional_weight_loss\", \"routine_nutritional_counselling_not_done_at_6_months\", \"assessment_tools\", \"patient_assessment\", \"patient_assessment_button\", \"patient_encounter_assessment\", \"encounter_assessment_table\", \"custom_flagging\", \"emergency_flagging\", \"airway_and_breathing\", \"weak_or_absent_breathing\", \"obstructed_breathing\", \"central_cyanosis_or_spo2\", \"severe_respiratory_distress\", \"circulation\", \"cold_hands_with\", \"weak_or_fast_pulse_gt_160\", \"capillary_return_time_gt_3_sec\", \"pulse_rate_lt_60_per_min\", \"coma_convulsing_confusions\", \"avpu_is_p_or_u_or_convulsion\", \"emergency_cbreak\", \"diarrhea\", \"diarrhea_with_sunken_eyes\", \"emergency_other\", \"anaphylaxis\", \"bulging_anterior_fontanelle\", \"intraosseous_line_in_place\", \"neck_stiffness\", \"artificial_airway\", \"hypothermia\", \"hypoglycemia\", \"immediate_post_ictal_period\", \"priority_flagging\", \"fast_breathing\", \"grunting\", \"chest_wall_indrawing\", \"wheeze\", \"stridor\", \"drooling\", \"priority_cbreak1\", \"tiny_child\", \"major_trauma\", \"pain\", \"poisoning\", \"severe_palmar_pallor\", \"restless_irritable_floppy\", \"priority_cbreak2\", \"referral\", \"malnutrition_severe_wasting\", \"oedeme_of_both_feet\", \"severe_burns\", \"unable_to_drink_or_vomits_anything\", \"custom_wellbaby\", \"immunization\", \"child_fit_for_vaccination\", \"visit_schedule\", \"vaccines\", \"height_for_age_chart\", \"vaccination_record\", \"received_vaccines\", \"wellbaby_audiology\", \"relative_with_ear_surgery\", \"relative_with_hearing_loss\", \"relative_with_hearing_devices\", \"wellbaby_audiology_column_break\", \"right_ear_results\", \"left_ear_results\", \"_followup_actions\", \"done_by\", \"growth_monitoring\", \"cord_stump_healing\", \"eyesight_and_hearing\", \"development_eyesight\", \"development_monitoring_column_break\", \"development_hearing\", \"child_milestone\", \"custom_payment\", \"payment_details_section\", \"sales_invoice\", \"mode_of_payment\", \"default_insurance\", \"suspended\", \"insurance_category_name\", \"insurance_category_employer\", \"payment_cb\", \"principal_member\", \"principal_member_name\", \"membership_no\", \"phone_number\", \"queue_group\", \"custom_history\", \"encounter_history\", \"patient_history\", \"custom_physical_examination\", \"physical_examinations\", \"get_kranium_history\", \"kranium_physican_exams_table\", \"kranium_data_response\", \"patient_physical_examination\", \"custom_diagnosis\", \"codification\", \"codification_table\", \"medical_diagnosis\", \"diagnosis_table\", \"nursing_diagnosis\", \"patient_nursing_diagnosis\", \"custom_plan_of_action\", \"plan_of_action\", \"patient_plan_of_action_notes\", \"is_outpatient\", \"physical_examination\", \"diagnosis\", \"diagnosis_in_print\", \"section_break_qjjp\", \"custom_services\", \"sb_test_prescription\", \"lab_test_prescription\", \"radiology\", \"radiology_details\", \"sb_procedures\", \"procedure_prescription\", \"custom_prescription\", \"sb_drug_prescription\", \"prescription_total\", \"drug_prescription\", \"prescription_details_section\", \"patient_info\", \"diag_field\", \"pharmacy_scrollable_table\", \"gch_standard_treatment_guideline_mapping\", \"gch_standard_treatment_guidelines\", \"gertrudes_standard_treatment_guidelines_med_doses\", \"pharmacy_dispensement_form\", \"allergies_doctor_view\", \"prescription_table\", \"custom_referrals\", \"service_referrals\", \"service_referral\", \"inpatient_service_referral_details\", \"is_referral\", \"referred_to_hospital\", \"referred_to_doctor\", \"treatment_given\", \"reason_for_referral\", \"enter_referral_reason\", \"consultant_comments\", \"rehabilitation_section\", \"therapy_plan\", \"therapies\", \"custom_tab_10\", \"section_break_210\", \"musculoskeletal\", \"sb_symptoms\", \"symptoms\", \"symptoms_in_print\", \"section_break_128\", \"get_applicable_treatment_plans\", \"section_break_33\", \"encounter_comment\", \"sb_refs\", \"triage_notes_cbreak\", \"nursing_notes\", \"patient_discharge_cbreak\", \"amended_from\", \"out_patient_discharge\", \"op_discharge_has_barrier_to_care\", \"outpatient_discharge_barriers_to_care_\", \"outpatient_discharge_barrier_to_care_notes\", \"op_conclusions_at_the_end_of_treatment\", \"wellbaby_child_safety_advice\", \"wellbaby_nutrition_counselling\", \"column_break_153\", \"op_discharge_has_education\", \"outpatient_discharge_patient_education\", \"outpatient_discharge_patient_education_notes\", \"outpatient_discharge_patients_condition_at_discharge\", \"wellbaby_advice_on_social_and_behavioral_development\", \"workflow_state\", \"encounter_reopen_details\", \"date_and_time\", \"reopen_reason\", \"reopened_by\", \"reclosing_date_and_time\", \"reclosed_by\", \"patient_details\", \"is_with_doctor\", \"data_fetched_from_prev_enc\", \"integrator\", \"auth_token\", \"number_of_emergency_vitals\", \"number_of_priority_vitals\", \"print_format_selector\", \"custom_section_break_wewje\", \"plan_of_action_notes\", \"view_results\", \"encounter_details_tab\", \"custom_section_break_rit9l\", \"order_history_html\", \"encounter_details\", \"notes_tab\", \"clinical_notes\", \"diagnosis_tab\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-25 11:21:48.849278", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-25 11:21:48.849278", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-links_order", "owner": "Administrator", "property": "links_order", "property_type": "Small Text", "row_name": null, "value": "[\"3b3fe1f240\", \"d0289nm734h\", \"6716137ed0\", \"a0614ea993\", \"a0516ea667\", \"a0883ea877\", \"3sdi8g89hn\", \"8dmn7jmlmo\", \"7ak0fapmit\", \"ucel4q3l1u\", \"i53m2n9j9r\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-11 12:31:09.482403", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.263610", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-naming_rule", "owner": "Administrator", "property": "naming_rule", "property_type": "Data", "row_name": null, "value": "Random"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-08-04 17:09:39.857656", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.865708", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-main-search_fields", "owner": "Administrator", "property": "search_fields", "property_type": "Data", "row_name": null, "value": "patient, medical_department, encounter_date, encounter_time"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-30 18:16:02.591488", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.876384", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-naming_series-depends_on", "owner": "Administrator", "property": "depends_on", "property_type": "Data", "row_name": null, "value": "eval:(doc.__islocal)"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-29 17:30:58.847957", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.886959", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-naming_series-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-14 12:46:53.146819", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.305201", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-naming_series-options", "owner": "Administrator", "property": "options", "property_type": "Text", "row_name": null, "value": "HLC-ENC-.YYYY.-"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-19 15:12:02.346641", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "notes_tab", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 15:12:02.346641", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-notes_tab-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-07 16:20:32.234894", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "notes_tab", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:39.315372", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-notes_tab-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "History"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-07-27 04:29:45.827174", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "patient_dob", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.897280", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_dob-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-08-22 11:13:52.787590", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "patient_name", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.907431", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-patient_name-depends_on", "owner": "Administrator", "property": "depends_on", "property_type": "Data", "row_name": null, "value": ""}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-03-24 16:53:49.999489", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "physical_examination", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.917748", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-physical_examination-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-26 21:47:13.293904", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "practitioner_name", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.928201", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-practitioner_name-in_list_view", "owner": "<EMAIL>", "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.028704", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "practitioner_name", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.938859", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-practitioner_name-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Doctor Name"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-26 21:47:13.154442", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "practitioner", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.949165", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-practitioner-in_standard_filter", "owner": "<EMAIL>", "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-08-04 21:49:51.503694", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "practitioner", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.959606", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-practitioner-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Primary Doctor"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-08-28 15:15:56.488618", "default_value": "0", "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "practitioner", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.969911", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-practitioner-reqd", "owner": "Administrator", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:57.531779", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rehabilitation_section", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.980297", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-rehabilitation_section-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.624875", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_drug_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:47.990374", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_drug_prescription-collapsible", "owner": "Administrator", "property": "collapsible", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-02-11 06:57:03.568240", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_drug_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.000612", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_drug_prescription-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Prescriptions"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-06-29 13:24:31.425757", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_procedures", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.011067", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_procedures-permlevel", "owner": "Administrator", "property": "permlevel", "property_type": "Int", "row_name": null, "value": "5"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:56.901501", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_symptoms", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.021610", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_symptoms-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-07-28 09:32:31.534333", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_test_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.033116", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_test_prescription-collapsible", "owner": "Administrator", "property": "collapsible", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-23 06:42:43.909478", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_test_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.043405", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_test_prescription-label", "owner": "Administrator", "property": "label", "property_type": "Data", "row_name": null, "value": "Laboratory"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2022-06-27 16:51:31.744555", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "sb_test_prescription", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.053610", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-sb_test_prescription-permlevel", "owner": "Administrator", "property": "permlevel", "property_type": "Int", "row_name": null, "value": "4"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-23 06:16:14.068156", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "symptoms_in_print", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.063776", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-symptoms_in_print-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:57.021055", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "symptoms", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.073852", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-symptoms-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2021-07-22 19:02:57.675063", "default_value": null, "doc_type": "Patient Encounter", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "therapies", "idx": 0, "is_system_generated": 0, "modified": "2025-03-19 16:37:48.084143", "modified_by": "Administrator", "module": null, "name": "Patient Encounter-therapies-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}], "sync_on_migrate": 1}